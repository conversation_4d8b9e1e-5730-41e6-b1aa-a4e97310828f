{"version": 3, "sources": ["../../../src/build/templates/edge-ssr-app.ts"], "names": ["ComponentMod", "nH<PERSON><PERSON>", "self", "Document", "appMod", "errorMod", "error500Mod", "maybeJSONParse", "str", "JSON", "parse", "undefined", "buildManifest", "__BUILD_MANIFEST", "prerenderManifest", "__PRERENDER_MANIFEST", "reactLoadableManifest", "__REACT_LOADABLE_MANIFEST", "rscManifest", "__RSC_MANIFEST", "rscServerManifest", "__RSC_SERVER_MANIFEST", "subresourceIntegrityManifest", "sriEnabled", "__SUBRESOURCE_INTEGRITY_MANIFEST", "nextFontManifest", "__NEXT_FONT_MANIFEST", "render", "getRender", "pagesType", "dev", "page", "pageMod", "renderToHTML", "clientReferenceManifest", "isServerComponent", "serverActionsManifest", "serverActions", "config", "nextConfig", "buildId", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "opts", "adapter", "IncrementalCache", "handler"], "mappings": ";;;;;;;;;;;;;;;IAoEaA,YAAY;eAAZA;;IAEb,OAMC;eANuBC;;;QAtEjB;yBACiB;wBACE;kCACO;2BAEoB;sEAC5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAgCLC;AAxBpB,0CAA0C;AAE1C,MAAMC,WAAyB;AAC/B,MAAMC,SAAS;AACf,MAAMC,WAAW;AACjB,MAAMC,cAAc;AAQpB,oBAAoB;AACpB,2BAA2B;AAC3B,aAAa;AACb,uBAAuB;AACvB,oBAAoB;AAEpB,MAAMC,iBAAiB,CAACC,MAAkBA,MAAMC,KAAKC,KAAK,CAACF,OAAOG;AAElE,MAAMC,gBAA+BV,KAAKW,gBAAgB;AAC1D,MAAMC,oBAAoBP,eAAeL,KAAKa,oBAAoB;AAClE,MAAMC,wBAAwBT,eAAeL,KAAKe,yBAAyB;AAC3E,MAAMC,eAAchB,uBAAAA,KAAKiB,cAAc,qBAAnBjB,oBAAqB,CAAC,WAAW;AACrD,MAAMkB,oBAAoBb,eAAeL,KAAKmB,qBAAqB;AACnE,MAAMC,+BAA+BC,aACjChB,eAAeL,KAAKsB,gCAAgC,IACpDb;AACJ,MAAMc,mBAAmBlB,eAAeL,KAAKwB,oBAAoB;AAEjE,MAAMC,SAASC,IAAAA,iBAAS,EAAC;IACvBC,WAAW;IACXC;IACAC,MAAM;IACN3B;IACA4B,SAAAA;IACA3B;IACAC;IACAH;IACAS;IACAE;IACAmB,cAAAA,+BAAY;IACZjB;IACAkB,yBAAyBC,oBAAoBjB,cAAc;IAC3DkB,uBAAuBD,oBAAoBf,oBAAoB;IAC/DiB,eAAeF,oBAAoBE,gBAAgB1B;IACnDW;IACAgB,QAAQC;IACRC,SAAS;IACTf;IACAgB;AACF;AAEO,MAAMzC,eAAegC;AAEb,SAAS/B,SAASyC,IAA4C;IAC3E,OAAOC,IAAAA,gBAAO,EAAC;QACb,GAAGD,IAAI;QACPE,kBAAAA,kCAAgB;QAChBC,SAASlB;IACX;AACF"}