{"version": 3, "sources": ["../../../../src/build/webpack/plugins/react-loadable-plugin.ts"], "names": ["ReactLoadablePlugin", "getModuleId", "compilation", "module", "chunkGraph", "getModuleFromDependency", "dep", "moduleGraph", "getModule", "getOriginModuleFromDependency", "getParentModule", "getChunkGroupFromBlock", "block", "getBlockChunkGroup", "buildManifest", "_compiler", "pagesDir", "dev", "manifest", "handleBlock", "blocks", "for<PERSON>ach", "chunkGroup", "dependency", "dependencies", "type", "startsWith", "originModule", "originRequest", "resource", "key", "path", "relative", "request", "files", "Set", "file", "add", "chunk", "chunks", "endsWith", "match", "id", "Array", "from", "modules", "Object", "keys", "sort", "reduce", "a", "c", "constructor", "opts", "filename", "runtimeAsset", "createAssets", "compiler", "assets", "sources", "RawSource", "JSON", "stringify", "apply", "hooks", "make", "tap", "processAssets", "name", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;AAmBA,GACA,oFAAoF;AACpF,sEAAsE;;;;;+BAiIzDA;;;eAAAA;;;yBA/HoB;6DAEhB;;;;;;AAEjB,SAASC,YAAYC,WAAgB,EAAEC,MAAW;IAChD,OAAOD,YAAYE,UAAU,CAACH,WAAW,CAACE;AAC5C;AAEA,SAASE,wBACPH,WAAgB,EAChBI,GAAQ;IAER,OAAOJ,YAAYK,WAAW,CAACC,SAAS,CAACF;AAC3C;AAEA,SAASG,8BACPP,WAAgB,EAChBI,GAAQ;IAER,OAAOJ,YAAYK,WAAW,CAACG,eAAe,CAACJ;AACjD;AAEA,SAASK,uBACPT,WAAgB,EAChBU,KAAU;IAEV,OAAOV,YAAYE,UAAU,CAACS,kBAAkB,CAACD;AACnD;AAEA,SAASE,cACPC,SAA2B,EAC3Bb,WAAgC,EAChCc,QAA4B,EAC5BC,GAAY;IAEZ,mDAAmD;IACnD,IAAI,CAACD,UAAU;QACb,OAAO,CAAC;IACV;IAEA,IAAIE,WAAsE,CAAC;IAE3E,mBAAmB;IACnB,0CAA0C;IAE1C,yBAAyB;IACzB,yEAAyE;IACzE,yDAAyD;IAEzD,sEAAsE;IACtE,MAAMC,cAAc,CAACP;QACnBA,MAAMQ,MAAM,CAACC,OAAO,CAACF;QACrB,MAAMG,aAAaX,uBAAuBT,aAAaU;QACvD,KAAK,MAAMW,cAAcX,MAAMY,YAAY,CAAE;YAC3C,IAAID,WAAWE,IAAI,CAACC,UAAU,CAAC,aAAa;gBAC1C,4BAA4B;gBAC5B,MAAMvB,SAASE,wBAAwBH,aAAaqB;gBACpD,IAAI,CAACpB,QAAQ;gBAEb,yCAAyC;gBACzC,MAAMwB,eAAelB,8BACnBP,aACAqB;gBAEF,MAAMK,gBAAoCD,gCAAAA,aAAcE,QAAQ;gBAChE,IAAI,CAACD,eAAe;gBAEpB,6DAA6D;gBAC7D,yDAAyD;gBACzD,0DAA0D;gBAC1D,MAAME,MAAM,CAAC,EAAEC,aAAI,CAACC,QAAQ,CAAChB,UAAUY,eAAe,IAAI,EACxDL,WAAWU,OAAO,CACnB,CAAC;gBAEF,4CAA4C;gBAC5C,MAAMC,QAAQ,IAAIC;gBAElB,IAAIjB,QAAQ,CAACY,IAAI,EAAE;oBACjB,iDAAiD;oBACjD,kDAAkD;oBAClD,gDAAgD;oBAChD,2CAA2C;oBAC3C,6CAA6C;oBAC7C,KAAK,MAAMM,QAAQlB,QAAQ,CAACY,IAAI,CAACI,KAAK,CAAE;wBACtCA,MAAMG,GAAG,CAACD;oBACZ;gBACF;gBAEA,oDAAoD;gBACpD,qDAAqD;gBACrD,6BAA6B;gBAC7B,IAAId,YAAY;oBACd,KAAK,MAAMgB,SAAS,AAAChB,WAClBiB,MAAM,CAAmC;wBAC1CD,MAAMJ,KAAK,CAACb,OAAO,CAAC,CAACe;4BACnB,IACE,AAACA,CAAAA,KAAKI,QAAQ,CAAC,UAAUJ,KAAKI,QAAQ,CAAC,OAAM,KAC7CJ,KAAKK,KAAK,CAAC,4BACX;gCACAP,MAAMG,GAAG,CAACD;4BACZ;wBACF;oBACF;gBACF;gBAEA,qDAAqD;gBACrD,sDAAsD;gBACtD,wDAAwD;gBAExD,uCAAuC;gBACvC,MAAMM,KAAKzB,MAAMa,MAAM7B,YAAYC,aAAaC;gBAChDe,QAAQ,CAACY,IAAI,GAAG;oBAAEY;oBAAIR,OAAOS,MAAMC,IAAI,CAACV;gBAAO;YACjD;QACF;IACF;IACA,KAAK,MAAM/B,UAAUD,YAAY2C,OAAO,CAAE;QACxC1C,OAAOiB,MAAM,CAACC,OAAO,CAACF;IACxB;IAEAD,WAAW4B,OAAOC,IAAI,CAAC7B,UACpB8B,IAAI,EACL,wCAAwC;KACvCC,MAAM,CAAC,CAACC,GAAGC,IAAO,CAAA,AAACD,CAAC,CAACC,EAAE,GAAGjC,QAAQ,CAACiC,EAAE,EAAGD,CAAAA,GAAI,CAAC;IAEhD,OAAOhC;AACT;AAEO,MAAMlB;IAMXoD,YAAYC,IAKX,CAAE;QACD,IAAI,CAACC,QAAQ,GAAGD,KAAKC,QAAQ;QAC7B,IAAI,CAACtC,QAAQ,GAAGqC,KAAKrC,QAAQ;QAC7B,IAAI,CAACuC,YAAY,GAAGF,KAAKE,YAAY;QACrC,IAAI,CAACtC,GAAG,GAAGoC,KAAKpC,GAAG;IACrB;IAEAuC,aAAaC,QAAa,EAAEvD,WAAgB,EAAEwD,MAAW,EAAE;QACzD,MAAMxC,WAAWJ,cACf2C,UACAvD,aACA,IAAI,CAACc,QAAQ,EACb,IAAI,CAACC,GAAG;QAEV,oDAAoD;QACpDyC,MAAM,CAAC,IAAI,CAACJ,QAAQ,CAAC,GAAG,IAAIK,gBAAO,CAACC,SAAS,CAC3CC,KAAKC,SAAS,CAAC5C,UAAU,MAAM;QAEjC,IAAI,IAAI,CAACqC,YAAY,EAAE;YACrBG,MAAM,CAAC,IAAI,CAACH,YAAY,CAAC,GAAG,IAAII,gBAAO,CAACC,SAAS,CAC/C,CAAC,+BAA+B,EAAEC,KAAKC,SAAS,CAC9CD,KAAKC,SAAS,CAAC5C,WACf,CAAC;QAEP;QACA,OAAOwC;IACT;IAEAK,MAAMN,QAA0B,EAAE;QAChCA,SAASO,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,yBAAyB,CAAChE;YAChDA,YAAY8D,KAAK,CAACG,aAAa,CAACD,GAAG,CACjC;gBACEE,MAAM;gBACNC,OAAOC,gBAAO,CAACC,WAAW,CAACC,8BAA8B;YAC3D,GACA,CAACd;gBACC,IAAI,CAACF,YAAY,CAACC,UAAUvD,aAAawD;YAC3C;QAEJ;IACF;AACF"}