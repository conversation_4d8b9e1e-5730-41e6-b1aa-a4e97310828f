{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/container/RuntimeError/ComponentStackFrameRow.tsx"], "names": ["ComponentStackFrameRow", "componentStackFrame", "component", "file", "lineNumber", "column", "open", "useOpenInEditor", "div", "data-nextjs-component-stack-frame", "h3", "tabIndex", "role", "onClick", "title", "span", "svg", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "path", "d", "polyline", "points", "line", "x1", "y1", "x2", "y2"], "mappings": ";;;;+BAIgBA;;;eAAAA;;;;gEAJE;iCAEc;AAEzB,SAASA,uBAAuB,KAItC;IAJsC,IAAA,EACrCC,qBAAqB,EAAEC,SAAS,EAAEC,IAAI,EAAEC,UAAU,EAAEC,MAAM,EAAE,EAG7D,GAJsC;IAKrC,MAAMC,OAAOC,IAAAA,gCAAe,EAAC;QAC3BJ;QACAE;QACAD;IACF;IAEA,qBACE,6BAACI;QAAIC,qCAAAA;qBACH,6BAACC,YAAIR,YACJC,qBACC,6BAACK;QACCG,UAAU;QACVC,MAAM;QACNC,SAASP;QACTQ,OAAO;qBAEP,6BAACC,cACEZ,MAAK,MAAGC,YAAW,KAAEC,QAAO,oBAE/B,6BAACW;QACCC,OAAM;QACNC,SAAQ;QACRC,MAAK;QACLC,QAAO;QACPC,aAAY;QACZC,eAAc;QACdC,gBAAe;qBAEf,6BAACC;QAAKC,GAAE;sBACR,6BAACC;QAASC,QAAO;sBACjB,6BAACC;QAAKC,IAAG;QAAKC,IAAG;QAAKC,IAAG;QAAKC,IAAG;WAGnC;AAGV"}