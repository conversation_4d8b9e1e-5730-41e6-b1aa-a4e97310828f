{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/container/Errors.tsx"], "names": ["Errors", "styles", "getErrorSignature", "ev", "event", "type", "ACTION_UNHANDLED_ERROR", "ACTION_UNHANDLED_REJECTION", "reason", "name", "message", "stack", "_", "errors", "initialDisplayState", "versionInfo", "lookups", "setLookups", "React", "useState", "readyErrors", "nextError", "useMemo", "ready", "next", "idx", "length", "e", "id", "push", "prev", "isLoading", "Boolean", "useEffect", "mounted", "getErrorByType", "then", "resolved", "m", "displayState", "setDisplayState", "activeIdx", "setActiveIndex", "previous", "useCallback", "preventDefault", "v", "Math", "max", "min", "activeError", "minimize", "hide", "fullscreen", "Overlay", "Toast", "className", "onClick", "div", "svg", "xmlns", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "circle", "cx", "cy", "r", "line", "x1", "y1", "x2", "y2", "span", "button", "data-nextjs-toast-errors-hide-button", "stopPropagation", "aria-label", "CloseIcon", "isServerError", "includes", "getErrorSource", "error", "Dialog", "aria-<PERSON>by", "aria-<PERSON><PERSON>", "onClose", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "LeftRightDialogHeader", "close", "small", "VersionStalenessInfo", "h1", "p", "HotlinkedText", "text", "DialogBody", "RuntimeError", "key", "toString", "css"], "mappings": ";;;;;;;;;;;;;;;IA0DaA,MAAM;eAANA;;IA6NAC,MAAM;eAANA;;;;;iEAvRU;qCAIhB;wBAUA;uCAC+B;yBACd;uBACF;gCACS;iCAEA;8BACH;2BACF;8BACG;sCACQ;+BAEP;;;;;;;;;;AAgB9B,SAASC,kBAAkBC,EAAuB;IAChD,MAAM,EAAEC,KAAK,EAAE,GAAGD;IAClB,OAAQC,MAAMC,IAAI;QAChB,KAAKC,2CAAsB;QAC3B,KAAKC,+CAA0B;YAAE;gBAC/B,OAAO,AAAGH,MAAMI,MAAM,CAACC,IAAI,GAAC,OAAIL,MAAMI,MAAM,CAACE,OAAO,GAAC,OAAIN,MAAMI,MAAM,CAACG,KAAK;YAC7E;QACA;YAAS,CACT;IACF;IAEA,6DAA6D;IAC7D,MAAMC,IAAWR;IACjB,OAAO;AACT;AAEO,MAAMJ,SAAgC,SAASA,OAAO,KAI5D;IAJ4D,IAAA,EAC3Da,MAAM,EACNC,mBAAmB,EACnBC,WAAW,EACZ,GAJ4D;IAK3D,MAAM,CAACC,SAASC,WAAW,GAAGC,OAAMC,QAAQ,CAC1C,CAAC;IAGH,MAAM,CAACC,aAAaC,UAAU,GAAGH,OAAMI,OAAO,CAE5C;QACA,IAAIC,QAA2B,EAAE;QACjC,IAAIC,OAAmC;QAEvC,6DAA6D;QAC7D,IAAK,IAAIC,MAAM,GAAGA,MAAMZ,OAAOa,MAAM,EAAE,EAAED,IAAK;YAC5C,MAAME,IAAId,MAAM,CAACY,IAAI;YACrB,MAAM,EAAEG,EAAE,EAAE,GAAGD;YACf,IAAIC,MAAMZ,SAAS;gBACjBO,MAAMM,IAAI,CAACb,OAAO,CAACY,GAAG;gBACtB;YACF;YAEA,6BAA6B;YAC7B,IAAIH,MAAM,GAAG;gBACX,MAAMK,OAAOjB,MAAM,CAACY,MAAM,EAAE;gBAC5B,IAAIvB,kBAAkB4B,UAAU5B,kBAAkByB,IAAI;oBACpD;gBACF;YACF;YAEAH,OAAOG;YACP;QACF;QAEA,OAAO;YAACJ;YAAOC;SAAK;IACtB,GAAG;QAACX;QAAQG;KAAQ;IAEpB,MAAMe,YAAYb,OAAMI,OAAO,CAAU;QACvC,OAAOF,YAAYM,MAAM,GAAG,KAAKM,QAAQnB,OAAOa,MAAM;IACxD,GAAG;QAACb,OAAOa,MAAM;QAAEN,YAAYM,MAAM;KAAC;IAEtCR,OAAMe,SAAS,CAAC;QACd,IAAIZ,aAAa,MAAM;YACrB;QACF;QACA,IAAIa,UAAU;QAEdC,IAAAA,8BAAc,EAACd,WAAWe,IAAI,CAC5B,CAACC;YACC,sEAAsE;YACtE,uEAAuE;YACvE,kBAAkB;YAClB,IAAIH,SAAS;gBACXjB,WAAW,CAACqB,IAAO,CAAA;wBAAE,GAAGA,CAAC;wBAAE,CAACD,SAAST,EAAE,CAAC,EAAES;oBAAS,CAAA;YACrD;QACF,GACA;QACE,yCAAyC;QAC3C;QAGF,OAAO;YACLH,UAAU;QACZ;IACF,GAAG;QAACb;KAAU;IAEd,MAAM,CAACkB,cAAcC,gBAAgB,GACnCtB,OAAMC,QAAQ,CAAeL;IAC/B,MAAM,CAAC2B,WAAWC,eAAe,GAAGxB,OAAMC,QAAQ,CAAS;IAC3D,MAAMwB,WAAWzB,OAAM0B,WAAW,CAAC,CAACjB;QAClCA,qBAAAA,EAAGkB,cAAc;QACjBH,eAAe,CAACI,IAAMC,KAAKC,GAAG,CAAC,GAAGF,IAAI;IACxC,GAAG,EAAE;IACL,MAAMtB,OAAON,OAAM0B,WAAW,CAC5B,CAACjB;QACCA,qBAAAA,EAAGkB,cAAc;QACjBH,eAAe,CAACI,IACdC,KAAKC,GAAG,CAAC,GAAGD,KAAKE,GAAG,CAAC7B,YAAYM,MAAM,GAAG,GAAGoB,IAAI;IAErD,GACA;QAAC1B,YAAYM,MAAM;KAAC;QAIdN;IADR,MAAM8B,cAAchC,OAAMI,OAAO,CAC/B,IAAMF,CAAAA,yBAAAA,WAAW,CAACqB,UAAU,YAAtBrB,yBAA0B,MAChC;QAACqB;QAAWrB;KAAY;IAG1B,kEAAkE;IAClE,gDAAgD;IAChDF,OAAMe,SAAS,CAAC;QACd,IAAIpB,OAAOa,MAAM,GAAG,GAAG;YACrBT,WAAW,CAAC;YACZuB,gBAAgB;YAChBE,eAAe;QACjB;IACF,GAAG;QAAC7B,OAAOa,MAAM;KAAC;IAElB,MAAMyB,WAAWjC,OAAM0B,WAAW,CAAC,CAACjB;QAClCA,qBAAAA,EAAGkB,cAAc;QACjBL,gBAAgB;IAClB,GAAG,EAAE;IACL,MAAMY,OAAOlC,OAAM0B,WAAW,CAAC,CAACjB;QAC9BA,qBAAAA,EAAGkB,cAAc;QACjBL,gBAAgB;IAClB,GAAG,EAAE;IACL,MAAMa,aAAanC,OAAM0B,WAAW,CAClC,CAACjB;QACCA,qBAAAA,EAAGkB,cAAc;QACjBL,gBAAgB;IAClB,GACA,EAAE;IAGJ,2EAA2E;IAC3E,6CAA6C;IAC7C,IAAI3B,OAAOa,MAAM,GAAG,KAAKwB,eAAe,MAAM;QAC5C,OAAO;IACT;IAEA,IAAInB,WAAW;QACb,6BAA6B;QAC7B,qBAAO,qBAACuB,gBAAO;IACjB;IAEA,IAAIf,iBAAiB,UAAU;QAC7B,OAAO;IACT;IAEA,IAAIA,iBAAiB,aAAa;QAChC,qBACE,qBAACgB,YAAK;YAACC,WAAU;YAA6BC,SAASJ;yBACrD,qBAACK;YAAIF,WAAU;yBACb,qBAACG;YACCC,OAAM;YACNC,OAAM;YACNC,QAAO;YACPC,SAAQ;YACRC,MAAK;YACLC,QAAO;YACPC,aAAY;YACZC,eAAc;YACdC,gBAAe;yBAEf,qBAACC;YAAOC,IAAG;YAAKC,IAAG;YAAKC,GAAE;0BAC1B,qBAACC;YAAKC,IAAG;YAAKC,IAAG;YAAIC,IAAG;YAAKC,IAAG;0BAChC,qBAACJ;YAAKC,IAAG;YAAKC,IAAG;YAAKC,IAAG;YAAQC,IAAG;2BAEtC,qBAACC,cACE1D,YAAYM,MAAM,EAAC,UAAON,YAAYM,MAAM,GAAG,IAAI,MAAM,mBAE5D,qBAACqD;YACCC,wCAAAA;YACAxB,WAAU;YACVnD,MAAK;YACLoD,SAAS,CAAC9B;gBACRA,EAAEsD,eAAe;gBACjB7B;YACF;YACA8B,cAAW;yBAEX,qBAACC,oBAAS;IAKpB;IAEA,MAAMC,gBAAgB;QAAC;QAAU;KAAc,CAACC,QAAQ,CACtDC,IAAAA,+BAAc,EAACpC,YAAYqC,KAAK,KAAK;IAGvC,qBACE,qBAACjC,gBAAO,sBACN,qBAACkC,cAAM;QACLnF,MAAK;QACLoF,mBAAgB;QAChBC,oBAAiB;QACjBC,SAASP,gBAAgBQ,YAAYzC;qBAErC,qBAAC0C,qBAAa,sBACZ,qBAACC,oBAAY;QAACtC,WAAU;qBACtB,qBAACuC,4CAAqB;QACpBpD,UAAUF,YAAY,IAAIE,WAAW;QACrCnB,MAAMiB,YAAYrB,YAAYM,MAAM,GAAG,IAAIF,OAAO;QAClDwE,OAAOZ,gBAAgBQ,YAAYzC;qBAEnC,qBAAC8C,6BACC,qBAACnB,cAAMrC,YAAY,IAAS,OAAI,mBAChC,qBAACqC,cAAM1D,YAAYM,MAAM,GAAQ,oBAChCN,YAAYM,MAAM,GAAG,IAAI,KAAK,MAEhCX,4BAAc,qBAACmF,0CAAoB,EAAKnF,eAAkB,qBAE7D,qBAACoF;QAAGvE,IAAG;OACJwD,gBAAgB,iBAAiB,0CAEpC,qBAACgB;QAAExE,IAAG;OACHsB,YAAYqC,KAAK,CAAC9E,IAAI,EAAC,KAAE,mBAC1B,qBAAC4F,4BAAa;QAACC,MAAMpD,YAAYqC,KAAK,CAAC7E,OAAO;SAE/C0E,8BACC,qBAAC1B,2BACC,qBAACuC,eAAM,gHAKPL,0BAEN,qBAACW,kBAAU;QAAC/C,WAAU;qBACpB,qBAACgD,0BAAY;QAACC,KAAKvD,YAAYtB,EAAE,CAAC8E,QAAQ;QAAInB,OAAOrC;;AAMjE;AAEO,MAAMjD,aAAS0G,kBAAG"}