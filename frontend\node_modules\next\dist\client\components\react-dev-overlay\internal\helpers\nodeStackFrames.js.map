{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/nodeStackFrames.ts"], "names": ["getFilesystemFrame", "getErrorSource", "decorateServerError", "getServerError", "frame", "f", "file", "startsWith", "test", "symbolError", "Symbol", "error", "type", "Object", "defineProperty", "writable", "enumerable", "configurable", "value", "n", "Error", "message", "e", "name", "stack", "toString", "parse", "map", "str", "methodName", "loc", "lineNumber", "column", "join"], "mappings": ";;;;;;;;;;;;;;;;;IAGgBA,kBAAkB;eAAlBA;;IAqBAC,cAAc;eAAdA;;IAMAC,mBAAmB;eAAnBA;;IASAC,cAAc;eAAdA;;;kCAvCM;AAGf,SAASH,mBAAmBI,KAAiB;IAClD,MAAMC,IAAgB;QAAE,GAAGD,KAAK;IAAC;IAEjC,IAAI,OAAOC,EAAEC,IAAI,KAAK,UAAU;QAC9B,IACE,SAAS;QACTD,EAAEC,IAAI,CAACC,UAAU,CAAC,QAClB,SAAS;QACT,aAAaC,IAAI,CAACH,EAAEC,IAAI,KACxB,aAAa;QACbD,EAAEC,IAAI,CAACC,UAAU,CAAC,SAClB;YACAF,EAAEC,IAAI,GAAG,AAAC,YAASD,EAAEC,IAAI;QAC3B;IACF;IAEA,OAAOD;AACT;AAEA,MAAMI,cAAcC,OAAO;AAEpB,SAAST,eAAeU,KAAY;IACzC,OAAO,AAACA,KAAa,CAACF,YAAY,IAAI;AACxC;AAIO,SAASP,oBAAoBS,KAAY,EAAEC,IAAe;IAC/DC,OAAOC,cAAc,CAACH,OAAOF,aAAa;QACxCM,UAAU;QACVC,YAAY;QACZC,cAAc;QACdC,OAAON;IACT;AACF;AAEO,SAAST,eAAeQ,KAAY,EAAEC,IAAe;IAC1D,IAAIO;IACJ,IAAI;QACF,MAAM,IAAIC,MAAMT,MAAMU,OAAO;IAC/B,EAAE,OAAOC,GAAG;QACVH,IAAIG;IACN;IAEAH,EAAEI,IAAI,GAAGZ,MAAMY,IAAI;IACnB,IAAI;QACFJ,EAAEK,KAAK,GAAG,AAAGL,EAAEM,QAAQ,KAAG,OAAIC,IAAAA,uBAAK,EAACf,MAAMa,KAAK,EAC5CG,GAAG,CAAC3B,oBACJ2B,GAAG,CAAC,CAACtB;YACJ,IAAIuB,MAAM,AAAC,YAASvB,EAAEwB,UAAU;YAChC,IAAIxB,EAAEC,IAAI,EAAE;gBACV,IAAIwB,MAAMzB,EAAEC,IAAI;gBAChB,IAAID,EAAE0B,UAAU,EAAE;oBAChBD,OAAO,AAAC,MAAGzB,EAAE0B,UAAU;oBACvB,IAAI1B,EAAE2B,MAAM,EAAE;wBACZF,OAAO,AAAC,MAAGzB,EAAE2B,MAAM;oBACrB;gBACF;gBACAJ,OAAO,AAAC,OAAIE,MAAI;YAClB;YACA,OAAOF;QACT,GACCK,IAAI,CAAC;IACV,EAAE,UAAM;QACNd,EAAEK,KAAK,GAAGb,MAAMa,KAAK;IACvB;IAEAtB,oBAAoBiB,GAAGP;IACvB,OAAOO;AACT"}