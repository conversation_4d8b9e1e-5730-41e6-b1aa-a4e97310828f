{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/getErrorByType.ts"], "names": ["getErrorByType", "ev", "id", "event", "type", "ACTION_UNHANDLED_ERROR", "ACTION_UNHANDLED_REJECTION", "readyRuntimeError", "runtime", "error", "reason", "frames", "getOriginalStackFrames", "getErrorSource", "toString", "componentStackFrames", "_", "Error"], "mappings": ";;;;+BAkBsBA;;;eAAAA;;;qCAff;iCAEwB;4BACQ;AAYhC,eAAeA,eACpBC,EAAuB;IAEvB,MAAM,EAAEC,EAAE,EAAEC,KAAK,EAAE,GAAGF;IACtB,OAAQE,MAAMC,IAAI;QAChB,KAAKC,2CAAsB;QAC3B,KAAKC,+CAA0B;YAAE;gBAC/B,MAAMC,oBAAuC;oBAC3CL;oBACAM,SAAS;oBACTC,OAAON,MAAMO,MAAM;oBACnBC,QAAQ,MAAMC,IAAAA,kCAAsB,EAClCT,MAAMQ,MAAM,EACZE,IAAAA,+BAAc,EAACV,MAAMO,MAAM,GAC3BP,MAAMO,MAAM,CAACI,QAAQ;gBAEzB;gBACA,IAAIX,MAAMC,IAAI,KAAKC,2CAAsB,EAAE;oBACzCE,kBAAkBQ,oBAAoB,GAAGZ,MAAMY,oBAAoB;gBACrE;gBACA,OAAOR;YACT;QACA;YAAS;gBACP;YACF;IACF;IACA,6DAA6D;IAC7D,MAAMS,IAAWb;IACjB,MAAM,IAAIc,MAAM;AAClB"}