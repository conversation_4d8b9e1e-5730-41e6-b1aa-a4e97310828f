{"version": 3, "sources": ["../../../src/build/webpack-build/impl.ts"], "names": ["webpackBuildImpl", "worker<PERSON>ain", "debug", "origDebug", "isTelemetryPlugin", "plugin", "TelemetryPlugin", "isTraceEntryPointsPlugin", "TraceEntryPointsPlugin", "compilerName", "result", "warnings", "errors", "stats", "webpackBuildStart", "nextBuildSpan", "NextBuildContext", "dir", "config", "runWebpackSpan", "<PERSON><PERSON><PERSON><PERSON>", "entrypoints", "traceAsyncFn", "createEntrypoints", "buildId", "envFiles", "loadedEnvFiles", "isDev", "rootDir", "pageExtensions", "pagesDir", "appDir", "pages", "mappedPages", "appPaths", "mappedAppPages", "previewMode", "previewProps", "rootPaths", "mappedRootPaths", "hasInstrumentationHook", "commonWebpackOptions", "isServer", "rewrites", "originalRewrites", "originalRedirects", "reactProductionProfiling", "noMangling", "clientRouterFilters", "previewModeId", "allowedRevalidateHeaderKeys", "fetchCacheKeyPrefix", "configs", "info", "loadProjectInfo", "dev", "Promise", "all", "getBaseWebpackConfig", "middlewareMatchers", "compilerType", "COMPILER_NAMES", "client", "server", "edgeServer", "clientConfig", "serverConfig", "edgeConfig", "optimization", "minimize", "minimizer", "length", "Log", "warn", "process", "hrtime", "clientResult", "serverResult", "edgeServerResult", "inputFileSystem", "start", "Date", "now", "runCompiler", "pluginState", "getPluginState", "key", "injectedClientEntries", "value", "clientEntry", "entry", "APP_CLIENT_INTERNALS", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "import", "layer", "WEBPACK_LAYERS", "appPagesBrowser", "dependOn", "purge", "concat", "filter", "nonNullable", "traceFn", "formatWebpackMessages", "telemetryPlugin", "plugins", "find", "traceEntryPointsPlugin", "webpackBuildEnd", "error", "Boolean", "join", "console", "red", "indexOf", "page_name_regex", "parsed", "exec", "page_name", "groups", "Error", "err", "code", "buildSpinner", "stopAndPersist", "event", "duration", "buildTraceContext", "workerData", "Object", "assign", "buildContext", "initializeTraceState", "traceState", "resumePluginState", "loadConfig", "PHASE_PRODUCTION_BUILD", "trace", "entriesTrace", "chunksTrace", "entryNameMap", "depModArray", "entryEntries", "entryNameFilesMap", "stop", "debugTraceEvents", "getTraceEvents"], "mappings": ";;;;;;;;;;;;;;;IA6DsBA,gBAAgB;eAAhBA;;IAmRAC,UAAU;eAAVA;;;4BA/UF;8EACc;6BACN;2BAOrB;0BACqB;6DACP;uEACiC;iCAEtB;8BAKzB;yBAC2B;+DACX;uBAOhB;4BACwB;4CACQ;8DAIjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtB,MAAMC,QAAQC,IAAAA,cAAS,EAAC;AAcxB,SAASC,kBAAkBC,MAAe;IACxC,OAAOA,kBAAkBC,gCAAe;AAC1C;AAEA,SAASC,yBACPF,MAAe;IAEf,OAAOA,kBAAkBG,kDAAsB;AACjD;AAEO,eAAeR,iBACpBS,YAA4C;QA+MT,uBAIJ;IA7M/B,IAAIC,SAAgC;QAClCC,UAAU,EAAE;QACZC,QAAQ,EAAE;QACVC,OAAO,EAAE;IACX;IACA,IAAIC;IACJ,MAAMC,gBAAgBC,8BAAgB,CAACD,aAAa;IACpD,MAAME,MAAMD,8BAAgB,CAACC,GAAG;IAChC,MAAMC,SAASF,8BAAgB,CAACE,MAAM;IAEtC,MAAMC,iBAAiBJ,cAAcK,UAAU,CAAC;IAChD,MAAMC,cAAc,MAAMN,cACvBK,UAAU,CAAC,sBACXE,YAAY,CAAC,IACZC,IAAAA,0BAAiB,EAAC;YAChBC,SAASR,8BAAgB,CAACQ,OAAO;YACjCN,QAAQA;YACRO,UAAUT,8BAAgB,CAACU,cAAc;YACzCC,OAAO;YACPC,SAASX;YACTY,gBAAgBX,OAAOW,cAAc;YACrCC,UAAUd,8BAAgB,CAACc,QAAQ;YACnCC,QAAQf,8BAAgB,CAACe,MAAM;YAC/BC,OAAOhB,8BAAgB,CAACiB,WAAW;YACnCC,UAAUlB,8BAAgB,CAACmB,cAAc;YACzCC,aAAapB,8BAAgB,CAACqB,YAAY;YAC1CC,WAAWtB,8BAAgB,CAACuB,eAAe;YAC3CC,wBAAwBxB,8BAAgB,CAACwB,sBAAsB;QACjE;IAGJ,MAAMC,uBAAuB;QAC3BC,UAAU;QACVlB,SAASR,8BAAgB,CAACQ,OAAO;QACjCN,QAAQA;QACRa,QAAQf,8BAAgB,CAACe,MAAM;QAC/BD,UAAUd,8BAAgB,CAACc,QAAQ;QACnCa,UAAU3B,8BAAgB,CAAC2B,QAAQ;QACnCC,kBAAkB5B,8BAAgB,CAAC4B,gBAAgB;QACnDC,mBAAmB7B,8BAAgB,CAAC6B,iBAAiB;QACrDC,0BAA0B9B,8BAAgB,CAAC8B,wBAAwB;QACnEC,YAAY/B,8BAAgB,CAAC+B,UAAU;QACvCC,qBAAqBhC,8BAAgB,CAACgC,mBAAmB;QACzDC,eAAejC,8BAAgB,CAACiC,aAAa;QAC7CC,6BAA6BlC,8BAAgB,CAACkC,2BAA2B;QACzEC,qBAAqBnC,8BAAgB,CAACmC,mBAAmB;IAC3D;IAEA,MAAMC,UAAU,MAAMjC,eACnBC,UAAU,CAAC,2BACXE,YAAY,CAAC;QACZ,MAAM+B,OAAO,MAAMC,IAAAA,8BAAe,EAAC;YACjCrC;YACAC,QAAQuB,qBAAqBvB,MAAM;YACnCqC,KAAK;QACP;QACA,OAAOC,QAAQC,GAAG,CAAC;YACjBC,IAAAA,sBAAoB,EAACzC,KAAK;gBACxB,GAAGwB,oBAAoB;gBACvBkB,oBAAoBtC,YAAYsC,kBAAkB;gBAClDxC;gBACAyC,cAAcC,yBAAc,CAACC,MAAM;gBACnCzC,aAAaA,YAAYyC,MAAM;gBAC/B,GAAGT,IAAI;YACT;YACAK,IAAAA,sBAAoB,EAACzC,KAAK;gBACxB,GAAGwB,oBAAoB;gBACvBtB;gBACAwC,oBAAoBtC,YAAYsC,kBAAkB;gBAClDC,cAAcC,yBAAc,CAACE,MAAM;gBACnC1C,aAAaA,YAAY0C,MAAM;gBAC/B,GAAGV,IAAI;YACT;YACAK,IAAAA,sBAAoB,EAACzC,KAAK;gBACxB,GAAGwB,oBAAoB;gBACvBtB;gBACAwC,oBAAoBtC,YAAYsC,kBAAkB;gBAClDC,cAAcC,yBAAc,CAACG,UAAU;gBACvC3C,aAAaA,YAAY2C,UAAU;gBACnC,GAAGX,IAAI;YACT;SACD;IACH;IAEF,MAAMY,eAAeb,OAAO,CAAC,EAAE;IAC/B,MAAMc,eAAed,OAAO,CAAC,EAAE;IAC/B,MAAMe,aAAaf,OAAO,CAAC,EAAE;IAE7B,IACEa,aAAaG,YAAY,IACxBH,CAAAA,aAAaG,YAAY,CAACC,QAAQ,KAAK,QACrCJ,aAAaG,YAAY,CAACE,SAAS,IAClCL,aAAaG,YAAY,CAACE,SAAS,CAACC,MAAM,KAAK,CAAC,GACpD;QACAC,KAAIC,IAAI,CACN,CAAC,iIAAiI,CAAC;IAEvI;IAEA3D,oBAAoB4D,QAAQC,MAAM;IAElCzE,MAAM,CAAC,iBAAiB,CAAC,EAAEO;IAC3B,+EAA+E;IAC/E,MAAMU,eAAeG,YAAY,CAAC;QAChC,qDAAqD;QACrD,8DAA8D;QAC9D,IAAIsD,eAA4C;QAEhD,uEAAuE;QACvE,yEAAyE;QACzE,IAAIC,eACF;QACF,IAAIC,mBAEO;QAEX,IAAIC;QAEJ,IAAI,CAACtE,gBAAgBA,iBAAiB,UAAU;YAC9CP,MAAM;YACN,MAAM8E,QAAQC,KAAKC,GAAG;YACrB,CAACL,cAAcE,gBAAgB,GAAG,MAAMI,IAAAA,qBAAW,EAACjB,cAAc;gBACjE/C;gBACA4D;YACF;YACA7E,MAAM,CAAC,yBAAyB,EAAE+E,KAAKC,GAAG,KAAKF,MAAM,EAAE,CAAC;QAC1D;QAEA,IAAI,CAACvE,gBAAgBA,iBAAiB,eAAe;YACnDP,MAAM;YACN,MAAM8E,QAAQC,KAAKC,GAAG;YACrB,CAACJ,kBAAkBC,gBAAgB,GAAGZ,aACnC,MAAMgB,IAAAA,qBAAW,EAAChB,YAAY;gBAAEhD;gBAAgB4D;YAAgB,KAChE;gBAAC;aAAK;YACV7E,MAAM,CAAC,8BAA8B,EAAE+E,KAAKC,GAAG,KAAKF,MAAM,EAAE,CAAC;QAC/D;QAEA,wCAAwC;QACxC,IAAI,EAACH,gCAAAA,aAAcjE,MAAM,CAAC2D,MAAM,KAAI,EAACO,oCAAAA,iBAAkBlE,MAAM,CAAC2D,MAAM,GAAE;YACpE,MAAMa,cAAcC,IAAAA,4BAAc;YAClC,IAAK,MAAMC,OAAOF,YAAYG,qBAAqB,CAAE;gBACnD,MAAMC,QAAQJ,YAAYG,qBAAqB,CAACD,IAAI;gBACpD,MAAMG,cAAcxB,aAAayB,KAAK;gBACtC,IAAIJ,QAAQK,+BAAoB,EAAE;oBAChCF,WAAW,CAACG,+CAAoC,CAAC,GAAG;wBAClDC,QAAQ;4BACN,6HAA6H;4BAC7H,oFAAoF;+BACjFJ,WAAW,CAACG,+CAAoC,CAAC,CAACC,MAAM;4BAC3DL;yBACD;wBACDM,OAAOC,0BAAc,CAACC,eAAe;oBACvC;gBACF,OAAO;oBACLP,WAAW,CAACH,IAAI,GAAG;wBACjBW,UAAU;4BAACL,+CAAoC;yBAAC;wBAChDC,QAAQL;wBACRM,OAAOC,0BAAc,CAACC,eAAe;oBACvC;gBACF;YACF;YAEA,IAAI,CAACvF,gBAAgBA,iBAAiB,UAAU;gBAC9CP,MAAM;gBACN,MAAM8E,QAAQC,KAAKC,GAAG;gBACrB,CAACN,cAAcG,gBAAgB,GAAG,MAAMI,IAAAA,qBAAW,EAAClB,cAAc;oBACjE9C;oBACA4D;gBACF;gBACA7E,MAAM,CAAC,yBAAyB,EAAE+E,KAAKC,GAAG,KAAKF,MAAM,EAAE,CAAC;YAC1D;QACF;QAEAD,gBAAgBmB,KAAK;QAErBxF,SAAS;YACPC,UAAU,AAAC,EAAE,CACVwF,MAAM,CACLvB,gCAAAA,aAAcjE,QAAQ,EACtBkE,gCAAAA,aAAclE,QAAQ,EACtBmE,oCAAAA,iBAAkBnE,QAAQ,EAE3ByF,MAAM,CAACC,wBAAW;YACrBzF,QAAQ,AAAC,EAAE,CACRuF,MAAM,CACLvB,gCAAAA,aAAchE,MAAM,EACpBiE,gCAAAA,aAAcjE,MAAM,EACpBkE,oCAAAA,iBAAkBlE,MAAM,EAEzBwF,MAAM,CAACC,wBAAW;YACrBxF,OAAO;gBACL+D,gCAAAA,aAAc/D,KAAK;gBACnBgE,gCAAAA,aAAchE,KAAK;gBACnBiE,oCAAAA,iBAAkBjE,KAAK;aACxB;QACH;IACF;IACAH,SAASK,cACNK,UAAU,CAAC,2BACXkF,OAAO,CAAC,IAAMC,IAAAA,8BAAqB,EAAC7F,QAAQ;IAE/CM,8BAAgB,CAACwF,eAAe,IAAG,wBAAA,AACjCvC,aACAwC,OAAO,qBAF0B,sBAExBC,IAAI,CAACtG;IAEhB,MAAMuG,0BAAyB,wBAAA,AAC7BzC,aACAuC,OAAO,qBAFsB,sBAEpBC,IAAI,CAACnG;IAEhB,MAAMqG,kBAAkBlC,QAAQC,MAAM,CAAC7D;IAEvC,IAAIJ,OAAOE,MAAM,CAAC2D,MAAM,GAAG,GAAG;QAC5B,8DAA8D;QAC9D,0DAA0D;QAC1D,IAAI7D,OAAOE,MAAM,CAAC2D,MAAM,GAAG,GAAG;YAC5B7D,OAAOE,MAAM,CAAC2D,MAAM,GAAG;QACzB;QACA,IAAIsC,QAAQnG,OAAOE,MAAM,CAACwF,MAAM,CAACU,SAASC,IAAI,CAAC;QAE/CC,QAAQH,KAAK,CAACI,IAAAA,eAAG,EAAC;QAElB,IACEJ,MAAMK,OAAO,CAAC,wBAAwB,CAAC,KACvCL,MAAMK,OAAO,CAAC,uCAAuC,CAAC,GACtD;YACA,MAAMC,kBAAkB;YACxB,MAAMC,SAASD,gBAAgBE,IAAI,CAACR;YACpC,MAAMS,YAAYF,UAAUA,OAAOG,MAAM,IAAIH,OAAOG,MAAM,CAACD,SAAS;YACpE,MAAM,IAAIE,MACR,CAAC,sFAAsF,EAAEF,UAAU,oFAAoF,CAAC;QAE5L;QAEAN,QAAQH,KAAK,CAACA;QACdG,QAAQH,KAAK;QAEb,IACEA,MAAMK,OAAO,CAAC,wBAAwB,CAAC,KACvCL,MAAMK,OAAO,CAAC,uBAAuB,CAAC,GACtC;YACA,MAAMO,MAAM,IAAID,MACd;YAEFC,IAAIC,IAAI,GAAG;YACX,MAAMD;QACR;QACA,MAAMA,MAAM,IAAID,MAAM;QACtBC,IAAIC,IAAI,GAAG;QACX,MAAMD;IACR,OAAO;QACL,IAAI/G,OAAOC,QAAQ,CAAC4D,MAAM,GAAG,GAAG;YAC9BC,KAAIC,IAAI,CAAC;YACTuC,QAAQvC,IAAI,CAAC/D,OAAOC,QAAQ,CAACyF,MAAM,CAACU,SAASC,IAAI,CAAC;YAClDC,QAAQvC,IAAI;QACd,OAAO,IAAI,CAAChE,cAAc;gBACxBO;aAAAA,iCAAAA,8BAAgB,CAAC2G,YAAY,qBAA7B3G,+BAA+B4G,cAAc;YAC7CpD,KAAIqD,KAAK,CAAC;QACZ;QAEA,OAAO;YACLC,UAAUlB,eAAe,CAAC,EAAE;YAC5BmB,iBAAiB,EAAEpB,0CAAAA,uBAAwBoB,iBAAiB;YAC5D3C,aAAaC,IAAAA,4BAAc;QAC7B;IACF;AACF;AAGO,eAAepF,WAAW+H,UAIhC;IAKC,0EAA0E;IAC1EC,OAAOC,MAAM,CAAClH,8BAAgB,EAAEgH,WAAWG,YAAY;IAEvD,0CAA0C;IAC1CC,IAAAA,2BAAoB,EAACJ,WAAWK,UAAU;IAE1C,sBAAsB;IACtBC,IAAAA,+BAAiB,EAACtH,8BAAgB,CAACoE,WAAW;IAE9C,iDAAiD;IACjDpE,8BAAgB,CAACE,MAAM,GAAG,MAAMqH,IAAAA,eAAU,EACxCC,iCAAsB,EACtBxH,8BAAgB,CAACC,GAAG;IAEtBD,8BAAgB,CAACD,aAAa,GAAG0H,IAAAA,YAAK,EACpC,CAAC,YAAY,EAAET,WAAWvH,YAAY,CAAC,CAAC;IAG1C,MAAMC,SAAS,MAAMV,iBAAiBgI,WAAWvH,YAAY;IAC7D,MAAM,EAAEiI,YAAY,EAAEC,WAAW,EAAE,GAAGjI,OAAOqH,iBAAiB,IAAI,CAAC;IACnE,IAAIW,cAAc;QAChB,MAAM,EAAEE,YAAY,EAAEC,WAAW,EAAE,GAAGH;QACtC,IAAIG,aAAa;YACfnI,OAAOqH,iBAAiB,CAAEW,YAAY,CAAEG,WAAW,GAAGA;QACxD;QACA,IAAID,cAAc;YAChB,MAAME,eAAeF;YACrBlI,OAAOqH,iBAAiB,CAAEW,YAAY,CAAEE,YAAY,GAAGE;QACzD;IACF;IACA,IAAIH,+BAAAA,YAAaI,iBAAiB,EAAE;QAClC,MAAMA,oBAAoBJ,YAAYI,iBAAiB;QACvDrI,OAAOqH,iBAAiB,CAAEY,WAAW,CAAEI,iBAAiB,GAAGA;IAC7D;IACA/H,8BAAgB,CAACD,aAAa,CAACiI,IAAI;IACnC,OAAO;QAAE,GAAGtI,MAAM;QAAEuI,kBAAkBC,IAAAA,qBAAc;IAAG;AACzD"}