{"version": 3, "sources": ["../../../../src/build/webpack/plugins/middleware-plugin.ts"], "names": ["MiddlewarePlugin", "SUPPORTED_NATIVE_MODULES", "getEdgePolyfilledModules", "handleWebpackExternalForEdgeRuntime", "KNOWN_SAFE_DYNAMIC_PACKAGES", "require", "NAME", "isUsingIndirectEvalAndUsedByExports", "args", "moduleGraph", "runtime", "module", "usingIndirectEval", "wp", "exportsInfo", "getExportsInfo", "exportName", "getUsed", "UsageState", "Unused", "getEntryFiles", "entryFiles", "meta", "opts", "files", "edgeSSR", "isServerComponent", "push", "SERVER_REFERENCE_MANIFEST", "sriEnabled", "SUBRESOURCE_INTEGRITY_MANIFEST", "filter", "file", "startsWith", "endsWith", "map", "replace", "CLIENT_REFERENCE_MANIFEST", "MIDDLEWARE_BUILD_MANIFEST", "MIDDLEWARE_REACT_LOADABLE_MANIFEST", "NEXT_FONT_MANIFEST", "NextBuildContext", "hasInstrumentationHook", "INSTRUMENTATION_HOOK_FILENAME", "process", "env", "NODE_ENV", "PRERENDER_MANIFEST", "getCreateAssets", "params", "compilation", "metadataByEntry", "assets", "middlewareManifest", "sortedMiddleware", "middleware", "functions", "version", "entrypoint", "entrypoints", "values", "metadata", "name", "get", "page", "edgeMiddleware", "edgeApiFunction", "matcherSource", "isAppDir", "normalizeAppPath", "catchAll", "namedRegex", "getNamedMiddlewareRegex", "matchers", "regexp", "originalSource", "edgeFunctionDefinition", "getFiles", "wasm", "Array", "from", "wasmBindings", "filePath", "assetBindings", "regions", "getSortedRoutes", "Object", "keys", "MIDDLEWARE_MANIFEST", "sources", "RawSource", "JSON", "stringify", "buildWebpackError", "message", "loc", "entryModule", "parser", "error", "compiler", "webpack", "WebpackError", "state", "current", "isInMiddlewareLayer", "layer", "isNodeJsModule", "moduleName", "builtinModules", "includes", "isDynamicCodeEvaluationAllowed", "fileName", "middlewareConfig", "rootDir", "some", "pkg", "path", "sep", "isMatch", "unstable_allowDynamicGlobs", "buildUnsupportedApiError", "apiName", "rest", "start", "line", "registerUnsupportedApiHooks", "expression", "EDGE_UNSUPPORTED_NODE_APIS", "warnForUnsupportedApi", "node", "warnings", "hooks", "call", "for", "tap", "callMemberChain", "expression<PERSON>ember<PERSON>hain", "warnForUnsupportedProcessApi", "callee", "getCodeAnalyzer", "dev", "handleExpression", "optimize", "InnerGraph", "onUsage", "used", "buildInfo", "getModuleBuildInfo", "Set", "handleWrapExpression", "expr", "ConstDependency", "dependencies", "dep1", "range", "addPresentationalDependency", "dep2", "handleWrapWasmCompileExpression", "handleWrapWasmInstantiateExpression", "handleImport", "source", "value", "importLocByPath", "Map", "importedModule", "toString", "set", "sourcePosition", "identifier", "sourceContent", "skip", "undefined", "prefix", "new", "importCall", "import", "getExtractMetadata", "clear", "telemetry", "traceGlobals", "entryName", "entry", "entries", "route", "options", "EDGE_RUNTIME_WEBPACK", "entryDependency", "getResolvedModule", "modules", "addEntriesFromDependency", "dependency", "getModule", "add", "for<PERSON>ach", "includeDependencies", "entryMetadata", "preferredRegion", "ogImageGenerationCount", "resource", "hasOGImageGeneration", "test", "util", "getEntryRuntime", "id", "record", "eventName", "payload", "absolutePagePath", "config", "fileWithDynamicCode", "userRequest", "errors", "join", "nextEdgeSSR", "nextEdgeMiddleware", "nextEdgeApiFunction", "nextWasmMiddlewareBinding", "nextAssetMiddlewareBinding", "conn", "getOutgoingConnections", "EVENT_BUILD_FEATURE_USAGE", "featureName", "invocationCount", "constructor", "apply", "normalModuleFactory", "codeAnalyzer", "finishModules", "tapPromise", "processAssets", "stage", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS", "supportedEdgePolyfills", "records", "mod", "request", "context", "contextInfo", "getResolve", "issuer<PERSON><PERSON>er", "has"], "mappings": ";;;;;;;;;;;;;;;;;IAksBA,OAwDC;eAxDoBA;;IA0DRC,wBAAwB;eAAxBA;;IAUGC,wBAAwB;eAAxBA;;IASMC,mCAAmC;eAAnCA;;;4BAzwBkB;oCACL;uBACH;yBACC;4BACT;6DACP;2BAYV;wBAGsB;wBACa;0BACT;4BACa;8BACb;;;;;;AAEjC,MAAMC,8BACJC,QAAQ;AA4BV,MAAMC,OAAO;AAEb;;;;CAIC,GACD,SAASC,oCAAoCC,IAM5C;IACC,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAEC,QAAAA,OAAM,EAAEC,iBAAiB,EAAEC,EAAE,EAAE,GAAGL;IAChE,IAAI,OAAOI,sBAAsB,WAAW;QAC1C,OAAOA;IACT;IAEA,MAAME,cAAcL,YAAYM,cAAc,CAACJ;IAC/C,KAAK,MAAMK,cAAcJ,kBAAmB;QAC1C,IAAIE,YAAYG,OAAO,CAACD,YAAYN,aAAaG,GAAGK,UAAU,CAACC,MAAM,EAAE;YACrE,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,SAASC,cACPC,UAAoB,EACpBC,IAAmB,EACnBC,IAEC;IAED,MAAMC,QAAkB,EAAE;IAC1B,IAAIF,KAAKG,OAAO,EAAE;QAChB,IAAIH,KAAKG,OAAO,CAACC,iBAAiB,EAAE;YAClCF,MAAMG,IAAI,CAAC,CAAC,OAAO,EAAEC,oCAAyB,CAAC,GAAG,CAAC;YACnD,IAAIL,KAAKM,UAAU,EAAE;gBACnBL,MAAMG,IAAI,CAAC,CAAC,OAAO,EAAEG,yCAA8B,CAAC,GAAG,CAAC;YAC1D;YACAN,MAAMG,IAAI,IACLN,WACAU,MAAM,CACL,CAACC,OACCA,KAAKC,UAAU,CAAC,WAAW,CAACD,KAAKE,QAAQ,CAAC,mBAE7CC,GAAG,CACF,CAACH,OACC,YACAA,KAAKI,OAAO,CAAC,OAAO,MAAMC,oCAAyB,GAAG;QAGhE;QAEAb,MAAMG,IAAI,CACR,CAAC,OAAO,EAAEW,oCAAyB,CAAC,GAAG,CAAC,EACxC,CAAC,OAAO,EAAEC,6CAAkC,CAAC,GAAG,CAAC;QAGnDf,MAAMG,IAAI,CAAC,CAAC,OAAO,EAAEa,6BAAkB,CAAC,GAAG,CAAC;IAC9C;IAEA,IAAIC,8BAAgB,CAAEC,sBAAsB,EAAE;QAC5ClB,MAAMG,IAAI,CAAC,CAAC,YAAY,EAAEgB,yCAA6B,CAAC,GAAG,CAAC;IAC9D;IAEA,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzCtB,MAAMG,IAAI,CAACoB,6BAAkB,CAACX,OAAO,CAAC,QAAQ;IAChD;IAEAZ,MAAMG,IAAI,IACLN,WACAU,MAAM,CAAC,CAACC,OAAS,CAACA,KAAKE,QAAQ,CAAC,mBAChCC,GAAG,CAAC,CAACH,OAAS,YAAYA;IAG/B,OAAOR;AACT;AAEA,SAASwB,gBAAgBC,MAMxB;IACC,MAAM,EAAEC,WAAW,EAAEC,eAAe,EAAE5B,IAAI,EAAE,GAAG0B;IAC/C,OAAO,CAACG;QACN,MAAMC,qBAAyC;YAC7CC,kBAAkB,EAAE;YACpBC,YAAY,CAAC;YACbC,WAAW,CAAC;YACZC,SAAS;QACX;QACA,KAAK,MAAMC,cAAcR,YAAYS,WAAW,CAACC,MAAM,GAAI;gBAQvDC,0BACAA,mBACAA,2BAKoBA,oBASLA;YAvBjB,IAAI,CAACH,WAAWI,IAAI,EAAE;gBACpB;YACF;YAEA,sDAAsD;YACtD,MAAMD,WAAWV,gBAAgBY,GAAG,CAACL,WAAWI,IAAI;YACpD,MAAME,OACJH,CAAAA,6BAAAA,2BAAAA,SAAUI,cAAc,qBAAxBJ,yBAA0BG,IAAI,MAC9BH,6BAAAA,oBAAAA,SAAUpC,OAAO,qBAAjBoC,kBAAmBG,IAAI,MACvBH,6BAAAA,4BAAAA,SAAUK,eAAe,qBAAzBL,0BAA2BG,IAAI;YACjC,IAAI,CAACA,MAAM;gBACT;YACF;YAEA,MAAMG,gBAAgBN,EAAAA,qBAAAA,SAASpC,OAAO,qBAAhBoC,mBAAkBO,QAAQ,IAC5CC,IAAAA,0BAAgB,EAACL,QACjBA;YAEJ,MAAMM,WAAW,CAACT,SAASpC,OAAO,IAAI,CAACoC,SAASK,eAAe;YAE/D,MAAM,EAAEK,UAAU,EAAE,GAAGC,IAAAA,mCAAuB,EAACL,eAAe;gBAC5DG;YACF;YACA,MAAMG,WAAWZ,CAAAA,6BAAAA,4BAAAA,SAAUI,cAAc,qBAAxBJ,0BAA0BY,QAAQ,KAAI;gBACrD;oBACEC,QAAQH;oBACRI,gBAAgBX,SAAS,OAAOM,WAAW,YAAYH;gBACzD;aACD;YAED,MAAMS,yBAAiD;gBACrDpD,OAAOJ,cAAcsC,WAAWmB,QAAQ,IAAIhB,UAAUtC;gBACtDuC,MAAMJ,WAAWI,IAAI;gBACrBE,MAAMA;gBACNS;gBACAK,MAAMC,MAAMC,IAAI,CAACnB,SAASoB,YAAY,EAAE,CAAC,CAACnB,MAAMoB,SAAS,GAAM,CAAA;wBAC7DpB;wBACAoB;oBACF,CAAA;gBACA9B,QAAQ2B,MAAMC,IAAI,CAACnB,SAASsB,aAAa,EAAE,CAAC,CAACrB,MAAMoB,SAAS,GAAM,CAAA;wBAChEpB;wBACAoB;oBACF,CAAA;gBACA,GAAIrB,SAASuB,OAAO,IAAI;oBAAEA,SAASvB,SAASuB,OAAO;gBAAC,CAAC;YACvD;YAEA,IAAIvB,SAASK,eAAe,IAAIL,SAASpC,OAAO,EAAE;gBAChD4B,mBAAmBG,SAAS,CAACQ,KAAK,GAAGY;YACvC,OAAO;gBACLvB,mBAAmBE,UAAU,CAACS,KAAK,GAAGY;YACxC;QACF;QAEAvB,mBAAmBC,gBAAgB,GAAG+B,IAAAA,sBAAe,EACnDC,OAAOC,IAAI,CAAClC,mBAAmBE,UAAU;QAG3CH,MAAM,CAACoC,8BAAmB,CAAC,GAAG,IAAIC,gBAAO,CAACC,SAAS,CACjDC,KAAKC,SAAS,CAACvC,oBAAoB,MAAM;IAE7C;AACF;AAEA,SAASwC,kBAAkB,EACzBC,OAAO,EACPC,GAAG,EACH7C,WAAW,EACX8C,WAAW,EACXC,MAAM,EAOP;IACC,MAAMC,QAAQ,IAAIhD,YAAYiD,QAAQ,CAACC,OAAO,CAACC,YAAY,CAACP;IAC5DI,MAAMpC,IAAI,GAAGxD;IACb,MAAMK,UAASqF,gBAAeC,0BAAAA,OAAQK,KAAK,CAACC,OAAO;IACnD,IAAI5F,SAAQ;QACVuF,MAAMvF,MAAM,GAAGA;IACjB;IACAuF,MAAMH,GAAG,GAAGA;IACZ,OAAOG;AACT;AAEA,SAASM,oBAAoBP,MAA2C;QAC/DA;IAAP,OAAOA,EAAAA,uBAAAA,OAAOK,KAAK,CAAC3F,MAAM,qBAAnBsF,qBAAqBQ,KAAK,MAAK;AACxC;AAEA,SAASC,eAAeC,UAAkB;IACxC,OAAOtG,QAAQ,UAAUuG,cAAc,CAACC,QAAQ,CAACF;AACnD;AAEA,SAASG,+BACPC,QAAgB,EAChBC,gBAAmC,EACnCC,OAAgB;IAEhB,wEAAwE;IACxE,2DAA2D;IAC3D,IACE7G,4BAA4B8G,IAAI,CAAC,CAACC,MAChCJ,SAASF,QAAQ,CAAC,CAAC,cAAc,EAAEM,IAAI,CAAC,CAAC,CAAC/E,OAAO,CAAC,OAAOgF,aAAI,CAACC,GAAG,KAEnE;QACA,OAAO;IACT;IAEA,MAAMvD,OAAOiD,SAAS3E,OAAO,CAAC6E,WAAW,IAAI;IAC7C,OAAOK,IAAAA,mBAAO,EAACxD,MAAMkD,CAAAA,oCAAAA,iBAAkBO,0BAA0B,KAAI,EAAE;AACzE;AAEA,SAASC,yBAAyB,EAChCC,OAAO,EACP1B,GAAG,EACH,GAAG2B,MAMJ;IACC,OAAO7B,kBAAkB;QACvBC,SAAS,CAAC,uBAAuB,EAAE2B,QAAQ,UAAU,EAAE1B,IAAI4B,KAAK,CAACC,IAAI,CAAC;8DACZ,CAAC;QAC3D7B;QACA,GAAG2B,IAAI;IACT;AACF;AAEA,SAASG,4BACP5B,MAA2C,EAC3C/C,WAAgC;IAEhC,KAAK,MAAM4E,cAAcC,qCAA0B,CAAE;QACnD,MAAMC,wBAAwB,CAACC;YAC7B,IAAI,CAACzB,oBAAoBP,SAAS;gBAChC;YACF;YACA/C,YAAYgF,QAAQ,CAACvG,IAAI,CACvB6F,yBAAyB;gBACvBtE;gBACA+C;gBACAwB,SAASK;gBACT,GAAGG,IAAI;YACT;YAEF,OAAO;QACT;QACAhC,OAAOkC,KAAK,CAACC,IAAI,CAACC,GAAG,CAACP,YAAYQ,GAAG,CAAChI,MAAM0H;QAC5C/B,OAAOkC,KAAK,CAACL,UAAU,CAACO,GAAG,CAACP,YAAYQ,GAAG,CAAChI,MAAM0H;QAClD/B,OAAOkC,KAAK,CAACI,eAAe,CACzBF,GAAG,CAACP,YACJQ,GAAG,CAAChI,MAAM0H;QACb/B,OAAOkC,KAAK,CAACK,qBAAqB,CAC/BH,GAAG,CAACP,YACJQ,GAAG,CAAChI,MAAM0H;IACf;IAEA,MAAMS,+BAA+B,CAACR,MAAW,CAACS,OAAiB;QACjE,IAAI,CAAClC,oBAAoBP,WAAWyC,WAAW,OAAO;YACpD;QACF;QACAxF,YAAYgF,QAAQ,CAACvG,IAAI,CACvB6F,yBAAyB;YACvBtE;YACA+C;YACAwB,SAAS,CAAC,QAAQ,EAAEiB,OAAO,CAAC;YAC5B,GAAGT,IAAI;QACT;QAEF,OAAO;IACT;IAEAhC,OAAOkC,KAAK,CAACI,eAAe,CACzBF,GAAG,CAAC,WACJC,GAAG,CAAChI,MAAMmI;IACbxC,OAAOkC,KAAK,CAACK,qBAAqB,CAC/BH,GAAG,CAAC,WACJC,GAAG,CAAChI,MAAMmI;AACf;AAEA,SAASE,gBAAgB1F,MAIxB;IACC,OAAO,CAACgD;QACN,MAAM,EACJ2C,GAAG,EACHzC,UAAU,EAAEC,SAASvF,EAAE,EAAE,EACzBqC,WAAW,EACZ,GAAGD;QACJ,MAAM,EAAEkF,KAAK,EAAE,GAAGlC;QAElB;;;;;KAKC,GACD,MAAM4C,mBAAmB;YACvB,IAAI,CAACrC,oBAAoBP,SAAS;gBAChC;YACF;YAEApF,GAAGiI,QAAQ,CAACC,UAAU,CAACC,OAAO,CAAC/C,OAAOK,KAAK,EAAE,CAAC2C,OAAO,IAAI;gBACvD,MAAMC,YAAYC,IAAAA,sCAAkB,EAAClD,OAAOK,KAAK,CAAC3F,MAAM;gBACxD,IAAIuI,UAAUtI,iBAAiB,KAAK,QAAQqI,SAAS,OAAO;oBAC1D;gBACF;gBAEA,IAAI,CAACC,UAAUtI,iBAAiB,IAAIqI,SAAS,MAAM;oBACjDC,UAAUtI,iBAAiB,GAAGqI;oBAC9B;gBACF;gBAEAC,UAAUtI,iBAAiB,GAAG,IAAIwI,IAAI;uBACjCrE,MAAMC,IAAI,CAACkE,UAAUtI,iBAAiB;uBACtCmE,MAAMC,IAAI,CAACiE;iBACf;YACH;QACF;QAEA;;;;KAIC,GACD,MAAMI,uBAAuB,CAACC;YAC5B,IAAI,CAAC9C,oBAAoBP,SAAS;gBAChC;YACF;YAEA,MAAM,EAAEsD,eAAe,EAAE,GAAG1I,GAAG2I,YAAY;YAC3C,MAAMC,OAAO,IAAIF,gBACf,sCACAD,KAAKI,KAAK,CAAC,EAAE;YAEfD,KAAK1D,GAAG,GAAGuD,KAAKvD,GAAG;YACnBE,OAAOK,KAAK,CAAC3F,MAAM,CAACgJ,2BAA2B,CAACF;YAChD,MAAMG,OAAO,IAAIL,gBAAgB,MAAMD,KAAKI,KAAK,CAAC,EAAE;YACpDE,KAAK7D,GAAG,GAAGuD,KAAKvD,GAAG;YACnBE,OAAOK,KAAK,CAAC3F,MAAM,CAACgJ,2BAA2B,CAACC;YAEhDf;YACA,OAAO;QACT;QAEA;;;;KAIC,GACD,MAAMgB,kCAAkC,CAACP;YACvC,IAAI,CAAC9C,oBAAoBP,SAAS;gBAChC;YACF;YAEA,MAAM,EAAEsD,eAAe,EAAE,GAAG1I,GAAG2I,YAAY;YAC3C,MAAMC,OAAO,IAAIF,gBACf,qDACAD,KAAKI,KAAK,CAAC,EAAE;YAEfD,KAAK1D,GAAG,GAAGuD,KAAKvD,GAAG;YACnBE,OAAOK,KAAK,CAAC3F,MAAM,CAACgJ,2BAA2B,CAACF;YAChD,MAAMG,OAAO,IAAIL,gBAAgB,MAAMD,KAAKI,KAAK,CAAC,EAAE;YACpDE,KAAK7D,GAAG,GAAGuD,KAAKvD,GAAG;YACnBE,OAAOK,KAAK,CAAC3F,MAAM,CAACgJ,2BAA2B,CAACC;YAEhDf;QACF;QAEA;;;;;;;;KAQC,GACD,MAAMiB,sCAAsC,CAACR;YAC3C,IAAI,CAAC9C,oBAAoBP,SAAS;gBAChC;YACF;YAEA,IAAI2C,KAAK;gBACP,MAAM,EAAEW,eAAe,EAAE,GAAG1I,GAAG2I,YAAY;gBAC3C,MAAMC,OAAO,IAAIF,gBACf,yDACAD,KAAKI,KAAK,CAAC,EAAE;gBAEfD,KAAK1D,GAAG,GAAGuD,KAAKvD,GAAG;gBACnBE,OAAOK,KAAK,CAAC3F,MAAM,CAACgJ,2BAA2B,CAACF;gBAChD,MAAMG,OAAO,IAAIL,gBAAgB,MAAMD,KAAKI,KAAK,CAAC,EAAE;gBACpDE,KAAK7D,GAAG,GAAGuD,KAAKvD,GAAG;gBACnBE,OAAOK,KAAK,CAAC3F,MAAM,CAACgJ,2BAA2B,CAACC;YAClD;QACF;QAEA;;KAEC,GACD,MAAMG,eAAe,CAAC9B;gBACeA;YAAnC,IAAIzB,oBAAoBP,aAAWgC,eAAAA,KAAK+B,MAAM,qBAAX/B,aAAagC,KAAK,MAAIhC,wBAAAA,KAAMlC,GAAG,GAAE;oBAO3CkC;gBANvB,MAAM,EAAEtH,QAAAA,OAAM,EAAEqJ,MAAM,EAAE,GAAG/D,OAAOK,KAAK;gBACvC,MAAM4C,YAAYC,IAAAA,sCAAkB,EAACxI;gBACrC,IAAI,CAACuI,UAAUgB,eAAe,EAAE;oBAC9BhB,UAAUgB,eAAe,GAAG,IAAIC;gBAClC;gBAEA,MAAMC,kBAAiBnC,qBAAAA,KAAK+B,MAAM,CAACC,KAAK,qBAAjBhC,mBAAmBoC,QAAQ;gBAClDnB,UAAUgB,eAAe,CAACI,GAAG,CAACF,gBAAgB;oBAC5CG,gBAAgB;wBACd,GAAGtC,KAAKlC,GAAG,CAAC4B,KAAK;wBACjBqC,QAAQrJ,QAAO6J,UAAU;oBAC3B;oBACAC,eAAeT,OAAOK,QAAQ;gBAChC;gBAEA,IAAI,CAACzB,OAAOlC,eAAe0D,iBAAiB;oBAC1ClH,YAAYgF,QAAQ,CAACvG,IAAI,CACvBkE,kBAAkB;wBAChBC,SAAS,CAAC,6BAA6B,EAAEsE,eAAe,UAAU,EAAEnC,KAAKlC,GAAG,CAAC4B,KAAK,CAACC,IAAI,CAAC;wEAC9B,CAAC;wBAC3D1E;wBACA+C;wBACA,GAAGgC,IAAI;oBACT;gBAEJ;YACF;QACF;QAEA;;;KAGC,GACD,MAAMyC,OAAO,IAAOlE,oBAAoBP,UAAU,OAAO0E;QAEzD,KAAK,MAAMC,UAAU;YAAC;YAAI;SAAU,CAAE;YACpCzC,MAAML,UAAU,CAACO,GAAG,CAAC,CAAC,EAAEuC,OAAO,kBAAkB,CAAC,EAAEtC,GAAG,CAAChI,MAAMoK;YAC9DvC,MAAML,UAAU,CAACO,GAAG,CAAC,CAAC,EAAEuC,OAAO,aAAa,CAAC,EAAEtC,GAAG,CAAChI,MAAMoK;YACzDvC,MAAMC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEuC,OAAO,IAAI,CAAC,EAAEtC,GAAG,CAAChI,MAAM+I;YAC1ClB,MAAMC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEuC,OAAO,QAAQ,CAAC,EAAEtC,GAAG,CAAChI,MAAM+I;YAC9ClB,MAAM0C,GAAG,CAACxC,GAAG,CAAC,CAAC,EAAEuC,OAAO,QAAQ,CAAC,EAAEtC,GAAG,CAAChI,MAAM+I;YAC7ClB,MAAMC,IAAI,CACPC,GAAG,CAAC,CAAC,EAAEuC,OAAO,mBAAmB,CAAC,EAClCtC,GAAG,CAAChI,MAAMuJ;YACb1B,MAAMC,IAAI,CACPC,GAAG,CAAC,CAAC,EAAEuC,OAAO,uBAAuB,CAAC,EACtCtC,GAAG,CAAChI,MAAMwJ;QACf;QAEA3B,MAAM2C,UAAU,CAACxC,GAAG,CAAChI,MAAMyJ;QAC3B5B,MAAM4C,MAAM,CAACzC,GAAG,CAAChI,MAAMyJ;QAEvB,IAAI,CAACnB,KAAK;YACR,8EAA8E;YAC9Ef,4BAA4B5B,QAAQ/C;QACtC;IACF;AACF;AAEA,SAAS8H,mBAAmB/H,MAK3B;IACC,MAAM,EAAE2F,GAAG,EAAE1F,WAAW,EAAEC,eAAe,EAAEgD,QAAQ,EAAE,GAAGlD;IACxD,MAAM,EAAEmD,SAASvF,EAAE,EAAE,GAAGsF;IACxB,OAAO;QACLhD,gBAAgB8H,KAAK;QACrB,MAAMC,YAAmCC,oBAAY,CAACpH,GAAG,CAAC;QAE1D,KAAK,MAAM,CAACqH,WAAWC,MAAM,IAAInI,YAAYoI,OAAO,CAAE;gBAK5BD,qBAsBpBE;YA1BJ,IAAIF,MAAMG,OAAO,CAAC9K,OAAO,KAAK+K,+BAAoB,EAAE;gBAElD;YACF;YACA,MAAMC,mBAAkBL,sBAAAA,MAAM7B,YAAY,qBAAlB6B,mBAAoB,CAAC,EAAE;YAC/C,MAAM,EAAEpE,OAAO,EAAEsE,KAAK,EAAE,GAAGpC,IAAAA,sCAAkB,EAC3CjG,YAAYzC,WAAW,CAACkL,iBAAiB,CAACD;YAG5C,MAAM,EAAEjL,WAAW,EAAE,GAAGyC;YACxB,MAAM0I,UAAU,IAAIxC;YACpB,MAAMyC,2BAA2B,CAACC;gBAChC,MAAMnL,UAASF,YAAYsL,SAAS,CAACD;gBACrC,IAAInL,SAAQ;oBACViL,QAAQI,GAAG,CAACrL;gBACd;YACF;YAEA0K,MAAM7B,YAAY,CAACyC,OAAO,CAACJ;YAC3BR,MAAMa,mBAAmB,CAACD,OAAO,CAACJ;YAElC,MAAMM,gBAA+B;gBACnClH,cAAc,IAAIkF;gBAClBhF,eAAe,IAAIgF;YACrB;YAEA,IAAIoB,0BAAAA,0BAAAA,MAAOvE,gBAAgB,qBAAvBuE,wBAAyBnG,OAAO,EAAE;gBACpC+G,cAAc/G,OAAO,GAAGmG,MAAMvE,gBAAgB,CAAC5B,OAAO;YACxD;YAEA,IAAImG,yBAAAA,MAAOa,eAAe,EAAE;gBAC1B,MAAMA,kBAAkBb,MAAMa,eAAe;gBAC7CD,cAAc/G,OAAO,GACnB,8DAA8D;gBAC9D,OAAOgH,oBAAoB,WACvB;oBAACA;iBAAgB,GACjBA;YACR;YAEA,IAAIC,yBAAyB;YAE7B,KAAK,MAAM1L,WAAUiL,QAAS;gBAC5B,MAAM1C,YAAYC,IAAAA,sCAAkB,EAACxI;gBAErC;;SAEC,GACD,IAAI,CAACiI,KAAK;oBACR,MAAM0D,WAAW3L,QAAO2L,QAAQ;oBAChC,MAAMC,uBACJD,YACA,oJAAoJE,IAAI,CACtJF;oBAGJ,IAAIC,sBAAsB;wBACxBF;oBACF;gBACF;gBAEA;;;;SAIC,GACD,IACE,CAACzD,OACDM,UAAUtI,iBAAiB,IAC3BL,oCAAoC;oBAClCI,QAAAA;oBACAF;oBACAC,SAASG,GAAG4L,IAAI,CAAC/L,OAAO,CAACgM,eAAe,CAACxJ,aAAakI;oBACtDxK,mBAAmBsI,UAAUtI,iBAAiB;oBAC9CC;gBACF,IACA;wBAKI0K;oBAJJ,MAAMoB,KAAKhM,QAAO6J,UAAU;oBAC5B,IAAI,uDAAuDgC,IAAI,CAACG,KAAK;wBACnE;oBACF;oBACA,IAAIpB,0BAAAA,2BAAAA,MAAOvE,gBAAgB,qBAAvBuE,yBAAyBhE,0BAA0B,EAAE;wBACvD2D,6BAAAA,UAAW0B,MAAM,CAAC;4BAChBC,WAAW;4BACXC,SAAS;gCACP9K,IAAI,EAAEuJ,yBAAAA,MAAOwB,gBAAgB,CAAC3K,OAAO,CAAC6E,WAAW,IAAI;gCACrD+F,MAAM,EAAEzB,yBAAAA,MAAOvE,gBAAgB;gCAC/BiG,qBAAqBtM,QAAOuM,WAAW,CAAC9K,OAAO,CAC7C6E,WAAW,IACX;4BAEJ;wBACF;oBACF;oBACA,IACE,CAACH,+BACCnG,QAAOuM,WAAW,EAClB3B,yBAAAA,MAAOvE,gBAAgB,EACvBC,UAEF;wBACA/D,YAAYiK,MAAM,CAACxL,IAAI,CACrBkE,kBAAkB;4BAChBC,SAAS,CAAC,0GAA0G,EAClH,OAAOoD,UAAUtI,iBAAiB,KAAK,YACnC,CAAC,UAAU,EAAEmE,MAAMC,IAAI,CAACkE,UAAUtI,iBAAiB,EAAEwM,IAAI,CACvD,MACA,CAAC,GACH,GACL,2EAA2E,CAAC;4BAC7EpH,aAAarF;4BACbuC;wBACF;oBAEJ;gBACF;gBAEA;;;SAGC,GACD,IAAIgG,6BAAAA,UAAWmE,WAAW,EAAE;oBAC1BlB,cAAc1K,OAAO,GAAGyH,UAAUmE,WAAW;gBAC/C,OAAO,IAAInE,6BAAAA,UAAWoE,kBAAkB,EAAE;oBACxCnB,cAAclI,cAAc,GAAGiF,UAAUoE,kBAAkB;gBAC7D,OAAO,IAAIpE,6BAAAA,UAAWqE,mBAAmB,EAAE;oBACzCpB,cAAcjI,eAAe,GAAGgF,UAAUqE,mBAAmB;gBAC/D;gBAEA;;;SAGC,GACD,IAAIrE,6BAAAA,UAAWsE,yBAAyB,EAAE;oBACxCrB,cAAclH,YAAY,CAACqF,GAAG,CAC5BpB,UAAUsE,yBAAyB,CAAC1J,IAAI,EACxCoF,UAAUsE,yBAAyB,CAACtI,QAAQ;gBAEhD;gBAEA,IAAIgE,6BAAAA,UAAWuE,0BAA0B,EAAE;oBACzCtB,cAAchH,aAAa,CAACmF,GAAG,CAC7BpB,UAAUuE,0BAA0B,CAAC3J,IAAI,EACzCoF,UAAUuE,0BAA0B,CAACvI,QAAQ;gBAEjD;gBAEA;;;SAGC,GACD,KAAK,MAAMwI,QAAQjN,YAAYkN,sBAAsB,CAAChN,SAAS;oBAC7D,IAAI+M,KAAK/M,MAAM,EAAE;wBACfiL,QAAQI,GAAG,CAAC0B,KAAK/M,MAAM;oBACzB;gBACF;YACF;YAEAuK,6BAAAA,UAAW0B,MAAM,CAAC;gBAChBC,WAAWe,iCAAyB;gBACpCd,SAAS;oBACPe,aAAa;oBACbC,iBAAiBzB;gBACnB;YACF;YACAlJ,gBAAgBmH,GAAG,CAACc,WAAWe;QACjC;IACF;AACF;AACe,MAAMnM;IAInB+N,YAAY,EAAEnF,GAAG,EAAE/G,UAAU,EAAyC,CAAE;QACtE,IAAI,CAAC+G,GAAG,GAAGA;QACX,IAAI,CAAC/G,UAAU,GAAGA;IACpB;IAEOmM,MAAM7H,QAA0B,EAAE;QACvCA,SAASgC,KAAK,CAACjF,WAAW,CAACoF,GAAG,CAAChI,MAAM,CAAC4C,aAAaD;YACjD,MAAM,EAAEkF,KAAK,EAAE,GAAGlF,OAAOgL,mBAAmB;YAC5C;;OAEC,GACD,MAAMC,eAAevF,gBAAgB;gBACnCC,KAAK,IAAI,CAACA,GAAG;gBACbzC;gBACAjD;YACF;YACAiF,MAAMlC,MAAM,CAACoC,GAAG,CAAC,mBAAmBC,GAAG,CAAChI,MAAM4N;YAC9C/F,MAAMlC,MAAM,CAACoC,GAAG,CAAC,sBAAsBC,GAAG,CAAChI,MAAM4N;YACjD/F,MAAMlC,MAAM,CAACoC,GAAG,CAAC,kBAAkBC,GAAG,CAAChI,MAAM4N;YAE7C;;OAEC,GACD,MAAM/K,kBAAkB,IAAIgH;YAC5BjH,YAAYiF,KAAK,CAACgG,aAAa,CAACC,UAAU,CACxC9N,MACA0K,mBAAmB;gBACjB9H;gBACAiD;gBACAyC,KAAK,IAAI,CAACA,GAAG;gBACbzF;YACF;YAGF;;OAEC,GACDD,YAAYiF,KAAK,CAACkG,aAAa,CAAC/F,GAAG,CACjC;gBACExE,MAAM;gBACNwK,OAAOlI,gBAAO,CAACmI,WAAW,CAACC,8BAA8B;YAC3D,GACAxL,gBAAgB;gBACdE;gBACAC;gBACA5B,MAAM;oBACJM,YAAY,IAAI,CAACA,UAAU;gBAC7B;YACF;QAEJ;IACF;AACF;AAEO,MAAM5B,2BAA2B;IACtC;IACA;IACA;IACA;IACA;CACD;AAED,MAAMwO,yBAAyB,IAAIrF,IAAYnJ;AAExC,SAASC;IACd,MAAMwO,UAAkC,CAAC;IACzC,KAAK,MAAMC,OAAO1O,yBAA0B;QAC1CyO,OAAO,CAACC,IAAI,GAAG,CAAC,cAAc,EAAEA,IAAI,CAAC;QACrCD,OAAO,CAAC,CAAC,KAAK,EAAEC,IAAI,CAAC,CAAC,GAAG,CAAC,cAAc,EAAEA,IAAI,CAAC;IACjD;IACA,OAAOD;AACT;AAEO,eAAevO,oCAAoC,EACxDyO,OAAO,EACPC,OAAO,EACPC,WAAW,EACXC,UAAU,EAMX;IACC,IACED,YAAYE,WAAW,KAAK,gBAC5BtI,eAAekI,YACf,CAACH,uBAAuBQ,GAAG,CAACL,UAC5B;QACA,wEAAwE;QACxE,IAAI;YACF,MAAMG,aAAaF,SAASD;QAC9B,EAAE,OAAM;YACN,OAAO,CAAC,uCAAuC,EAAEA,QAAQ,EAAE,CAAC;QAC9D;IACF;AACF"}