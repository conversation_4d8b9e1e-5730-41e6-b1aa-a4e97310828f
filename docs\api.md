# Wuzhen-MOSS-TTSD API 文档

## 1. API 概述

### 1.1 基础信息

```yaml
Base URL: https://api.wuzhen-moss-ttsd.com/api/v1
Authentication: <PERSON><PERSON> (JWT)
Content-Type: application/json
API Version: v1.0.0
```

### 1.2 通用响应格式

#### 成功响应
```json
{
  "success": true,
  "data": {
    // 具体数据
  },
  "message": "操作成功",
  "timestamp": "2025-01-01T00:00:00Z"
}
```

#### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": "详细错误信息"
  },
  "timestamp": "2025-01-01T00:00:00Z"
}
```

### 1.3 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 422 | 参数验证失败 |
| 429 | 请求频率限制 |
| 500 | 服务器内部错误 |

## 2. 认证接口

### 2.1 获取访问令牌

```http
POST /auth/token
```

**请求参数：**
```json
{
  "username": "string",
  "password": "string"
}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "bearer",
    "expires_in": 3600,
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### 2.2 刷新令牌

```http
POST /auth/refresh
```

**请求头：**
```
Authorization: Bearer <refresh_token>
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 3600
  }
}
```

## 3. 音频生成接口

### 3.1 对话语音生成

```http
POST /audio/dialogue
```

**请求参数：**
```json
{
  "text": "[S1]你好，今天天气真不错。[S2]是的，很适合出去走走。",
  "voice_settings": {
    "speed": 1.0,
    "pitch": 1.0,
    "volume": 1.0
  },
  "output_format": "wav",
  "quality": "high",
  "use_normalize": true
}
```

**参数说明：**
- `text` (string, required): 对话文本，使用 [S1] 和 [S2] 标记说话人
- `voice_settings` (object, optional): 语音设置
  - `speed` (float): 语速，范围 0.5-2.0，默认 1.0
  - `pitch` (float): 音调，范围 0.5-2.0，默认 1.0
  - `volume` (float): 音量，范围 0.1-2.0，默认 1.0
- `output_format` (string): 输出格式，支持 wav/mp3，默认 wav
- `quality` (string): 音质，支持 low/medium/high，默认 high
- `use_normalize` (boolean): 是否使用文本规范化，默认 true

**响应示例：**
```json
{
  "success": true,
  "data": {
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "status": "pending",
    "estimated_duration": 120,
    "created_at": "2025-01-01T00:00:00Z"
  }
}
```

### 3.2 语音克隆生成

```http
POST /audio/voice-clone
```

**请求参数：**
```json
{
  "text": "[S1]你好，我是克隆的声音。[S2]这真是太神奇了！",
  "speaker1": {
    "reference_audio": "file_id_or_url",
    "reference_text": "参考音频对应的文本"
  },
  "speaker2": {
    "reference_audio": "file_id_or_url", 
    "reference_text": "参考音频对应的文本"
  },
  "voice_settings": {
    "similarity": 0.8,
    "stability": 0.7
  },
  "output_format": "wav",
  "use_normalize": true
}
```

**参数说明：**
- `text` (string, required): 对话文本
- `speaker1/speaker2` (object, required): 说话人配置
  - `reference_audio` (string): 参考音频文件ID或URL
  - `reference_text` (string): 参考音频对应的文本
- `voice_settings` (object, optional): 克隆设置
  - `similarity` (float): 相似度，范围 0.1-1.0，默认 0.8
  - `stability` (float): 稳定性，范围 0.1-1.0，默认 0.7

**响应示例：**
```json
{
  "success": true,
  "data": {
    "task_id": "550e8400-e29b-41d4-a716-446655440001",
    "status": "pending",
    "estimated_duration": 180
  }
}
```

### 3.3 长音频生成

```http
POST /audio/long-form
```

**请求参数：**
```json
{
  "text": "这是一段很长的文本内容，需要转换为对话形式的音频...",
  "segment_length": 2000,
  "voice_settings": {
    "speed": 1.0,
    "style": "conversational"
  },
  "output_format": "wav",
  "use_normalize": true
}
```

**参数说明：**
- `text` (string, required): 长文本内容
- `segment_length` (integer): 分段长度，默认 2000 字符
- `voice_settings` (object, optional): 语音设置
  - `style` (string): 对话风格，支持 conversational/formal/casual

**响应示例：**
```json
{
  "success": true,
  "data": {
    "task_id": "550e8400-e29b-41d4-a716-446655440002",
    "status": "pending",
    "estimated_duration": 300,
    "segments_count": 5
  }
}
```

## 4. 播客生成接口

### 4.1 播客生成

```http
POST /podcast/generate
```

**请求参数：**
```json
{
  "content_source": "url",
  "content_data": "https://example.com/article",
  "podcast_style": "conversational",
  "language": "zh",
  "duration_target": 600,
  "hosts": {
    "host1": {
      "name": "主持人A",
      "voice_style": "professional"
    },
    "host2": {
      "name": "主持人B", 
      "voice_style": "casual"
    }
  },
  "output_format": "mp3"
}
```

**请求参数（文件上传）：**
```http
POST /podcast/generate
Content-Type: multipart/form-data

content_source=file
file=<PDF文件>
podcast_style=interview
language=zh
```

**参数说明：**
- `content_source` (string, required): 内容源类型
  - `text`: 直接文本
  - `url`: 网页链接
  - `file`: 上传文件（PDF）
- `content_data` (string): 内容数据（当 content_source 为 text 或 url 时）
- `podcast_style` (string): 播客风格
  - `conversational`: 对话式
  - `interview`: 访谈式
  - `narrative`: 叙述式
- `language` (string): 语言，支持 zh/en，默认 zh
- `duration_target` (integer): 目标时长（秒），可选
- `hosts` (object): 主持人配置
- `output_format` (string): 输出格式，默认 mp3

**响应示例：**
```json
{
  "success": true,
  "data": {
    "task_id": "550e8400-e29b-41d4-a716-446655440003",
    "status": "pending",
    "estimated_duration": 400,
    "content_summary": "文章主要讨论了人工智能的发展趋势..."
  }
}
```

## 5. 任务管理接口

### 5.1 查询任务状态

```http
GET /tasks/{task_id}
```

**路径参数：**
- `task_id` (string): 任务ID

**响应示例：**
```json
{
  "success": true,
  "data": {
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "type": "dialogue_audio",
    "status": "processing",
    "progress": 0.65,
    "current_stage": "audio_generation",
    "estimated_remaining": 45,
    "created_at": "2025-01-01T00:00:00Z",
    "updated_at": "2025-01-01T00:02:30Z",
    "metadata": {
      "input_length": 150,
      "segments_processed": 3,
      "total_segments": 5
    }
  }
}
```

**任务状态说明：**
- `pending`: 等待处理
- `processing`: 处理中
- `completed`: 已完成
- `failed`: 处理失败
- `cancelled`: 已取消

### 5.2 获取任务结果

```http
GET /tasks/{task_id}/result
```

**响应示例（成功）：**
```json
{
  "success": true,
  "data": {
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "status": "completed",
    "result": {
      "audio_url": "https://api.wuzhen-moss-ttsd.com/files/download/audio_123.wav",
      "duration": 45.6,
      "file_size": 2048000,
      "format": "wav",
      "sample_rate": 24000,
      "channels": 1
    },
    "metadata": {
      "processing_time": 120,
      "model_version": "v0.5",
      "text_normalized": true
    },
    "completed_at": "2025-01-01T00:05:00Z"
  }
}
```

**响应示例（失败）：**
```json
{
  "success": false,
  "data": {
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "status": "failed",
    "error": {
      "code": "AUDIO_GENERATION_FAILED",
      "message": "音频生成失败",
      "details": "GPU内存不足"
    },
    "failed_at": "2025-01-01T00:03:00Z"
  }
}
```

### 5.3 取消任务

```http
DELETE /tasks/{task_id}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "status": "cancelled",
    "cancelled_at": "2025-01-01T00:02:00Z"
  }
}
```

### 5.4 查询任务列表

```http
GET /tasks
```

**查询参数：**
- `status` (string): 任务状态过滤
- `type` (string): 任务类型过滤
- `page` (integer): 页码，默认 1
- `size` (integer): 每页大小，默认 20
- `sort` (string): 排序字段，默认 created_at
- `order` (string): 排序方向，asc/desc，默认 desc

**示例请求：**
```http
GET /tasks?status=completed&type=dialogue_audio&page=1&size=10
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "tasks": [
      {
        "task_id": "550e8400-e29b-41d4-a716-446655440000",
        "type": "dialogue_audio",
        "status": "completed",
        "created_at": "2025-01-01T00:00:00Z",
        "completed_at": "2025-01-01T00:05:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 10,
      "total": 25,
      "pages": 3
    }
  }
}
```

## 6. 文件管理接口

### 6.1 文件上传

```http
POST /files/upload
```

**请求格式：**
```http
Content-Type: multipart/form-data

file=<文件>
purpose=reference_audio
metadata={"description": "参考音频文件"}
```

**参数说明：**
- `file` (file, required): 上传的文件
- `purpose` (string, required): 文件用途
  - `reference_audio`: 参考音频
  - `document`: 文档文件
  - `other`: 其他
- `metadata` (string, optional): 文件元数据（JSON字符串）

**支持的文件格式：**
- 音频：wav, mp3, flac, m4a
- 文档：pdf, txt, docx
- 最大文件大小：100MB

**响应示例：**
```json
{
  "success": true,
  "data": {
    "file_id": "file_550e8400-e29b-41d4-a716-446655440000",
    "filename": "reference_audio.wav",
    "size": 1024000,
    "format": "wav",
    "duration": 30.5,
    "upload_url": "https://api.wuzhen-moss-ttsd.com/files/file_550e8400-e29b-41d4-a716-446655440000",
    "uploaded_at": "2025-01-01T00:00:00Z"
  }
}
```

### 6.2 文件下载

```http
GET /files/{file_id}
```

**查询参数：**
- `download` (boolean): 是否强制下载，默认 false

**响应：**
- 成功：返回文件内容
- 失败：返回错误信息

### 6.3 文件信息

```http
GET /files/{file_id}/info
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "file_id": "file_550e8400-e29b-41d4-a716-446655440000",
    "filename": "reference_audio.wav",
    "size": 1024000,
    "format": "wav",
    "duration": 30.5,
    "purpose": "reference_audio",
    "metadata": {
      "description": "参考音频文件",
      "sample_rate": 24000,
      "channels": 1
    },
    "uploaded_at": "2025-01-01T00:00:00Z"
  }
}
```

### 6.4 删除文件

```http
DELETE /files/{file_id}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "file_id": "file_550e8400-e29b-41d4-a716-446655440000",
    "deleted_at": "2025-01-01T00:00:00Z"
  }
}
```

## 7. WebSocket 实时通信

### 7.1 连接任务进度推送

```javascript
// 连接 WebSocket
const ws = new WebSocket('wss://api.wuzhen-moss-ttsd.com/ws/tasks/550e8400-e29b-41d4-a716-446655440000');

// 监听消息
ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('Task progress:', data);
};
```

**消息格式：**
```json
{
  "type": "progress",
  "task_id": "550e8400-e29b-41d4-a716-446655440000",
  "progress": 0.65,
  "stage": "audio_generation",
  "message": "正在生成音频...",
  "timestamp": "2025-01-01T00:02:30Z"
}
```

**消息类型：**
- `progress`: 进度更新
- `completed`: 任务完成
- `failed`: 任务失败
- `cancelled`: 任务取消

### 7.2 系统状态推送

```javascript
// 连接系统状态
const ws = new WebSocket('wss://api.wuzhen-moss-ttsd.com/ws/system');
```

**消息格式：**
```json
{
  "type": "system_status",
  "data": {
    "queue_length": 5,
    "active_tasks": 3,
    "gpu_utilization": 0.75,
    "memory_usage": 0.60
  },
  "timestamp": "2025-01-01T00:00:00Z"
}
```

## 8. 系统接口

### 8.1 健康检查

```http
GET /health
```

**响应示例：**
```json
{
  "status": "healthy",
  "service": "wuzhen-moss-ttsd",
  "version": "1.0.0",
  "timestamp": "2025-01-01T00:00:00Z",
  "checks": {
    "database": "healthy",
    "redis": "healthy",
    "gpu": "healthy",
    "storage": "healthy"
  }
}
```

### 8.2 系统指标

```http
GET /metrics
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "system": {
      "cpu_usage": 0.45,
      "memory_usage": 0.60,
      "disk_usage": 0.30,
      "gpu_usage": 0.75
    },
    "application": {
      "active_tasks": 3,
      "queue_length": 5,
      "completed_tasks_today": 150,
      "error_rate": 0.02
    },
    "performance": {
      "avg_response_time": 250,
      "avg_processing_time": 120,
      "throughput": 25
    }
  }
}
```

### 8.3 系统配置

```http
GET /system/config
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "max_file_size": 104857600,
    "supported_formats": ["wav", "mp3", "flac"],
    "max_concurrent_tasks": 10,
    "task_timeout": 3600,
    "rate_limits": {
      "requests_per_minute": 60,
      "tasks_per_hour": 100
    }
  }
}
```

## 9. 错误码说明

### 9.1 通用错误码

| 错误码 | 说明 |
|--------|------|
| INVALID_REQUEST | 请求格式错误 |
| MISSING_PARAMETER | 缺少必需参数 |
| INVALID_PARAMETER | 参数值无效 |
| UNAUTHORIZED | 未授权访问 |
| FORBIDDEN | 禁止访问 |
| RESOURCE_NOT_FOUND | 资源不存在 |
| RATE_LIMIT_EXCEEDED | 请求频率超限 |
| INTERNAL_ERROR | 服务器内部错误 |

### 9.2 业务错误码

| 错误码 | 说明 |
|--------|------|
| TEXT_TOO_LONG | 文本长度超限 |
| INVALID_AUDIO_FORMAT | 音频格式不支持 |
| AUDIO_GENERATION_FAILED | 音频生成失败 |
| VOICE_CLONE_FAILED | 语音克隆失败 |
| SCRIPT_GENERATION_FAILED | 脚本生成失败 |
| FILE_UPLOAD_FAILED | 文件上传失败 |
| TASK_NOT_FOUND | 任务不存在 |
| TASK_ALREADY_COMPLETED | 任务已完成 |
| INSUFFICIENT_RESOURCES | 系统资源不足 |
| MODEL_LOADING_FAILED | 模型加载失败 |

## 10. SDK 和示例

### 10.1 Python SDK 示例

```python
import requests
import time

class WuzhenMOSSTTSDClient:
    def __init__(self, api_key, base_url="https://api.wuzhen-moss-ttsd.com/api/v1"):
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def generate_dialogue_audio(self, text, **kwargs):
        """生成对话音频"""
        data = {"text": text, **kwargs}
        response = requests.post(
            f"{self.base_url}/audio/dialogue",
            json=data,
            headers=self.headers
        )
        return response.json()
    
    def get_task_status(self, task_id):
        """获取任务状态"""
        response = requests.get(
            f"{self.base_url}/tasks/{task_id}",
            headers=self.headers
        )
        return response.json()
    
    def wait_for_completion(self, task_id, timeout=300):
        """等待任务完成"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            result = self.get_task_status(task_id)
            status = result["data"]["status"]
            
            if status == "completed":
                return self.get_task_result(task_id)
            elif status == "failed":
                raise Exception(f"Task failed: {result['data'].get('error', {}).get('message')}")
            
            time.sleep(2)
        
        raise TimeoutError("Task timeout")
    
    def get_task_result(self, task_id):
        """获取任务结果"""
        response = requests.get(
            f"{self.base_url}/tasks/{task_id}/result",
            headers=self.headers
        )
        return response.json()

# 使用示例
client = WuzhenMOSSTTSDClient("your-api-key")

# 生成对话音频
task = client.generate_dialogue_audio(
    text="[S1]你好，今天天气真不错。[S2]是的，很适合出去走走。",
    use_normalize=True
)

# 等待完成
result = client.wait_for_completion(task["data"]["task_id"])
print(f"Audio URL: {result['data']['result']['audio_url']}")
```

### 10.2 JavaScript SDK 示例

```javascript
class WuzhenMOSSTTSDClient {
    constructor(apiKey, baseUrl = 'https://api.wuzhen-moss-ttsd.com/api/v1') {
        this.apiKey = apiKey;
        this.baseUrl = baseUrl;
        this.headers = {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
        };
    }

    async generateDialogueAudio(text, options = {}) {
        const response = await fetch(`${this.baseUrl}/audio/dialogue`, {
            method: 'POST',
            headers: this.headers,
            body: JSON.stringify({ text, ...options })
        });
        return response.json();
    }

    async getTaskStatus(taskId) {
        const response = await fetch(`${this.baseUrl}/tasks/${taskId}`, {
            headers: this.headers
        });
        return response.json();
    }

    async waitForCompletion(taskId, timeout = 300000) {
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            const result = await this.getTaskStatus(taskId);
            const status = result.data.status;
            
            if (status === 'completed') {
                return this.getTaskResult(taskId);
            } else if (status === 'failed') {
                throw new Error(`Task failed: ${result.data.error?.message}`);
            }
            
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
        throw new Error('Task timeout');
    }

    async getTaskResult(taskId) {
        const response = await fetch(`${this.baseUrl}/tasks/${taskId}/result`, {
            headers: this.headers
        });
        return response.json();
    }

    // WebSocket 连接
    connectTaskProgress(taskId, onProgress) {
        const ws = new WebSocket(`wss://api.wuzhen-moss-ttsd.com/ws/tasks/${taskId}`);
        
        ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            onProgress(data);
        };
        
        return ws;
    }
}

// 使用示例
const client = new WuzhenMOSSTTSDClient('your-api-key');

async function generateAudio() {
    try {
        // 生成音频
        const task = await client.generateDialogueAudio(
            '[S1]你好，今天天气真不错。[S2]是的，很适合出去走走。',
            { use_normalize: true }
        );
        
        // 监听进度
        const ws = client.connectTaskProgress(task.data.task_id, (progress) => {
            console.log(`Progress: ${progress.progress * 100}%`);
        });
        
        // 等待完成
        const result = await client.waitForCompletion(task.data.task_id);
        console.log('Audio URL:', result.data.result.audio_url);
        
        ws.close();
    } catch (error) {
        console.error('Error:', error);
    }
}

generateAudio();
```

### 10.3 cURL 示例

```bash
# 1. 获取访问令牌
curl -X POST "https://api.wuzhen-moss-ttsd.com/api/v1/auth/token" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "your-username",
    "password": "your-password"
  }'

# 2. 生成对话音频
curl -X POST "https://api.wuzhen-moss-ttsd.com/api/v1/audio/dialogue" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "[S1]你好，今天天气真不错。[S2]是的，很适合出去走走。",
    "use_normalize": true
  }'

# 3. 查询任务状态
curl -X GET "https://api.wuzhen-moss-ttsd.com/api/v1/tasks/TASK_ID" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 4. 获取任务结果
curl -X GET "https://api.wuzhen-moss-ttsd.com/api/v1/tasks/TASK_ID/result" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 5. 上传参考音频
curl -X POST "https://api.wuzhen-moss-ttsd.com/api/v1/files/upload" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@reference_audio.wav" \
  -F "purpose=reference_audio"

# 6. 生成语音克隆音频
curl -X POST "https://api.wuzhen-moss-ttsd.com/api/v1/audio/voice-clone" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "[S1]你好，我是克隆的声音。[S2]这真是太神奇了！",
    "speaker1": {
      "reference_audio": "FILE_ID",
      "reference_text": "参考音频对应的文本"
    },
    "speaker2": {
      "reference_audio": "FILE_ID",
      "reference_text": "参考音频对应的文本"
    }
  }'
```

## 11. 最佳实践

### 11.1 性能优化建议

1. **批量处理**: 对于多个短音频，建议合并为长音频一次性处理
2. **缓存结果**: 相同输入的结果可以缓存，避免重复计算
3. **异步处理**: 使用 WebSocket 监听进度，避免轮询
4. **文件预处理**: 上传前对音频文