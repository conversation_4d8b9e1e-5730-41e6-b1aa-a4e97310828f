{"name": "@types/howler", "version": "2.2.12", "description": "TypeScript definitions for howler", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/howler", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "xperiments", "url": "https://github.com/xperiments"}, {"name": "<PERSON>", "githubUsername": "alien35", "url": "https://github.com/alien35"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/nicholashza"}, {"name": "<PERSON>", "githubUsername": "cjurango", "url": "https://github.com/cjurango"}, {"name": "<PERSON>", "githubUsername": "Mr<PERSON><PERSON>s", "url": "https://github.com/MrGriefs"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/howler"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "f120574b702588e8e37db94f2a21a0874219513e1a0c9c53e221eba3775c5fb5", "typeScriptVersion": "4.8"}