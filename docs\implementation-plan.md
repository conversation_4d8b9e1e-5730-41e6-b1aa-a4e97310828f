# Wuzhen-MOSS-TTSD 项目实施计划

## 1. 项目初始化

### 1.1 创建项目结构

```bash
# 在 MOSS-TTSD 同级目录创建新项目
mkdir wuzhen-moss-ttsd
cd wuzhen-moss-ttsd

# 创建基础目录结构
mkdir -p src/{api,core,graphs,agents,adapters,models,services,utils}
mkdir -p src/api/routes
mkdir -p config
mkdir -p tests/{unit,integration,e2e}
mkdir -p docs
mkdir -p scripts
mkdir -p docker
```

### 1.2 集成 MOSS-TTSD 作为子模块

```bash
# 方案1: Git 子模块 (推荐)
git submodule add <moss-ttsd-repo-url> moss_ttsd

# 方案2: 直接复制 (开发阶段)
cp -r ../MOSS-TTSD ./moss_ttsd
```

### 1.3 创建核心配置文件

#### pyproject.toml
```toml
[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "wuzhen-moss-ttsd"
version = "0.1.0"
description = "智能语音生成系统基于MOSS-TTSD"
authors = [{name = "Wuzhen Team"}]
license = {text = "Apache-2.0"}
readme = "README.md"
requires-python = ">=3.10"

dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "langgraph>=0.0.40",
    "langchain>=0.1.0",
    "pydantic>=2.5.0",
    "sqlalchemy>=2.0.0",
    "alembic>=1.13.0",
    "redis>=5.0.0",
    "celery>=5.3.0",
    "httpx>=0.25.0",
    "python-multipart>=0.0.6",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "aiofiles>=23.2.1",
    "structlog>=23.2.0",
    "prometheus-client>=0.19.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.7.0",
    "pylint>=3.0.0",
    "pre-commit>=3.5.0",
]

[tool.black]
line-length = 88
target-version = ['py310']

[tool.isort]
profile = "black"
multi_line_output = 3

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --cov=src --cov-report=html --cov-report=term-missing"
```

#### requirements.txt
```txt
# 核心依赖
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
langgraph>=0.0.40
langchain>=0.1.0
pydantic>=2.5.0
sqlalchemy>=2.0.0
alembic>=1.13.0
redis>=5.0.0
celery>=5.3.0
httpx>=0.25.0
python-multipart>=0.0.6
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
aiofiles>=23.2.1
structlog>=23.2.0
prometheus-client>=0.19.0

# MOSS-TTSD 依赖
torch>=2.0.0
torchaudio>=2.0.0
transformers==4.53.2
accelerate>=0.20.0
numpy>=1.21.0
soundfile
librosa
tqdm
requests
openai
PyYAML
einops
huggingface_hub
liger_kernel
pydub

# 开发依赖
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
black>=23.0.0
isort>=5.12.0
mypy>=1.7.0
pylint>=3.0.0
pre-commit>=3.5.0
```

## 2. 核心代码结构

### 2.1 FastAPI 应用入口

#### src/main.py
```python
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
import structlog
from contextlib import asynccontextmanager

from src.core.config import settings
from src.core.database import init_db
from src.core.redis import init_redis
from src.api.routes import audio, podcast, tasks, files
from src.core.exceptions import setup_exception_handlers

logger = structlog.get_logger()

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时初始化
    logger.info("Starting Wuzhen-MOSS-TTSD...")
    await init_db()
    await init_redis()
    yield
    # 关闭时清理
    logger.info("Shutting down Wuzhen-MOSS-TTSD...")

app = FastAPI(
    title="Wuzhen-MOSS-TTSD",
    description="智能语音生成系统",
    version="0.1.0",
    lifespan=lifespan
)

# 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
app.add_middleware(GZipMiddleware, minimum_size=1000)

# 异常处理
setup_exception_handlers(app)

# 路由
app.include_router(audio.router, prefix="/api/v1/audio", tags=["audio"])
app.include_router(podcast.router, prefix="/api/v1/podcast", tags=["podcast"])
app.include_router(tasks.router, prefix="/api/v1/tasks", tags=["tasks"])
app.include_router(files.router, prefix="/api/v1/files", tags=["files"])

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "wuzhen-moss-ttsd"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "src.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG
    )
```

### 2.2 核心配置

#### src/core/config.py
```python
from pydantic_settings import BaseSettings
from typing import List, Optional
import os

class Settings(BaseSettings):
    # 基础配置
    DEBUG: bool = False
    SECRET_KEY: str = "your-secret-key-here"
    ALLOWED_HOSTS: List[str] = ["*"]
    
    # 数据库配置
    DATABASE_URL: str = "postgresql://user:pass@localhost:5432/wuzhen_moss"
    REDIS_URL: str = "redis://localhost:6379"
    
    # MOSS-TTSD 配置
    MOSS_TTSD_MODEL_PATH: str = "fnlp/MOSS-TTSD-v0.5"
    MOSS_TTSD_SPT_CONFIG: str = "moss_ttsd/XY_Tokenizer/config/xy_tokenizer_config.yaml"
    MOSS_TTSD_SPT_CHECKPOINT: str = "moss_ttsd/XY_Tokenizer/weights/xy_tokenizer.ckpt"
    
    # 硅基流动配置
    SILICONFLOW_API_KEY: str = ""
    SILICONFLOW_API_BASE: str = "https://api.siliconflow.cn/v1"
    
    # 文件存储配置
    UPLOAD_DIR: str = "uploads"
    OUTPUT_DIR: str = "outputs"
    MAX_FILE_SIZE: int = 100 * 1024 * 1024  # 100MB
    
    # 任务配置
    MAX_CONCURRENT_TASKS: int = 10
    TASK_TIMEOUT: int = 3600  # 1小时
    
    class Config:
        env_file = ".env"

settings = Settings()
```

### 2.3 MOSS-TTSD 适配器

#### src/adapters/moss_ttsd.py
```python
import torch
import torchaudio
from typing import Optional, Dict, Any, List
import structlog
from pathlib import Path

# 导入 MOSS-TTSD 模块
import sys
sys.path.append(str(Path(__file__).parent.parent.parent / "moss_ttsd"))

from generation_utils import load_model, process_batch
from src.core.config import settings

logger = structlog.get_logger()

class MOSSTTSDAdapter:
    """MOSS-TTSD 适配器"""
    
    def __init__(self):
        self.tokenizer = None
        self.model = None
        self.spt = None
        self.device = None
        self._initialized = False
    
    async def initialize(self):
        """初始化模型"""
        if self._initialized:
            return
            
        logger.info("Initializing MOSS-TTSD model...")
        
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        self.tokenizer, self.model, self.spt = load_model(
            settings.MOSS_TTSD_MODEL_PATH,
            settings.MOSS_TTSD_SPT_CONFIG,
            settings.MOSS_TTSD_SPT_CHECKPOINT
        )
        
        self.spt = self.spt.to(self.device)
        self.model = self.model.to(self.device)
        
        self._initialized = True
        logger.info("MOSS-TTSD model initialized successfully")
    
    async def generate_audio(
        self,
        text: str,
        reference_audio: Optional[str] = None,
        reference_text: Optional[str] = None,
        use_normalize: bool = True
    ) -> Dict[str, Any]:
        """生成音频"""
        if not self._initialized:
            await self.initialize()
        
        # 构建输入项
        item = {"text": text}
        
        if reference_audio and reference_text:
            item["prompt_audio"] = reference_audio
            item["prompt_text"] = reference_text
        
        try:
            # 处理批次
            actual_texts_data, audio_results = process_batch(
                batch_items=[item],
                tokenizer=self.tokenizer,
                model=self.model,
                spt=self.spt,
                device=self.device,
                system_prompt="You are a speech synthesizer that generates natural, realistic, and human-like conversational audio from dialogue text.",
                start_idx=0,
                use_normalize=use_normalize
            )
            
            if not audio_results or audio_results[0] is None:
                raise Exception("Audio generation failed")
            
            audio_result = audio_results[0]
            
            return {
                "audio_data": audio_result["audio_data"],
                "sample_rate": audio_result["sample_rate"],
                "metadata": {
                    "original_text": actual_texts_data[0]["original_text"],
                    "final_text": actual_texts_data[0]["final_text"],
                    "use_normalize": actual_texts_data[0]["use_normalize"]
                }
            }
            
        except Exception as e:
            logger.error("Audio generation failed", error=str(e))
            raise
    
    async def generate_voice_clone_audio(
        self,
        text: str,
        speaker1_audio: str,
        speaker1_text: str,
        speaker2_audio: str,
        speaker2_text: str,
        use_normalize: bool = True
    ) -> Dict[str, Any]:
        """生成语音克隆音频"""
        if not self._initialized:
            await self.initialize()
        
        item = {
            "text": text,
            "prompt_audio_speaker1": speaker1_audio,
            "prompt_text_speaker1": speaker1_text,
            "prompt_audio_speaker2": speaker2_audio,
            "prompt_text_speaker2": speaker2_text
        }
        
        try:
            actual_texts_data, audio_results = process_batch(
                batch_items=[item],
                tokenizer=self.tokenizer,
                model=self.model,
                spt=self.spt,
                device=self.device,
                system_prompt="You are a speech synthesizer that generates natural, realistic, and human-like conversational audio from dialogue text.",
                start_idx=0,
                use_normalize=use_normalize
            )
            
            if not audio_results or audio_results[0] is None:
                raise Exception("Voice clone audio generation failed")
            
            audio_result = audio_results[0]
            
            return {
                "audio_data": audio_result["audio_data"],
                "sample_rate": audio_result["sample_rate"],
                "metadata": {
                    "original_text": actual_texts_data[0]["original_text"],
                    "final_text": actual_texts_data[0]["final_text"],
                    "use_normalize": actual_texts_data[0]["use_normalize"]
                }
            }
            
        except Exception as e:
            logger.error("Voice clone audio generation failed", error=str(e))
            raise

# 全局实例
moss_ttsd_adapter = MOSSTTSDAdapter()
```

### 2.4 硅基流动适配器

#### src/adapters/siliconflow.py
```python
import httpx
from typing import Dict, Any, Optional
import structlog
from src.core.config import settings

logger = structlog.get_logger()

class SiliconFlowAdapter:
    """硅基流动大模型适配器"""
    
    def __init__(self):
        self.api_key = settings.SILICONFLOW_API_KEY
        self.api_base = settings.SILICONFLOW_API_BASE
        self.client = httpx.AsyncClient(
            base_url=self.api_base,
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            },
            timeout=60.0
        )
    
    async def generate_script(
        self,
        content: str,
        script_type: str = "dialogue",
        language: str = "zh",
        style: str = "conversational"
    ) -> str:
        """生成脚本"""
        
        # 根据脚本类型选择提示词
        prompts = {
            "dialogue": self._get_dialogue_prompt(language, style),
            "podcast": self._get_podcast_prompt(language, style),
            "long_audio": self._get_long_audio_prompt(language, style)
        }
        
        system_prompt = prompts.get(script_type, prompts["dialogue"])
        
        try:
            response = await self.client.post(
                "/chat/completions",
                json={
                    "model": "Qwen/Qwen2.5-7B-Instruct",
                    "messages": [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": content}
                    ],
                    "temperature": 0.7,
                    "max_tokens": 4000
                }
            )
            
            response.raise_for_status()
            result = response.json()
            
            return result["choices"][0]["message"]["content"]
            
        except Exception as e:
            logger.error("Script generation failed", error=str(e))
            raise
    
    def _get_dialogue_prompt(self, language: str, style: str) -> str:
        """获取对话脚本提示词"""
        if language == "zh":
            return """你是一个专业的对话脚本生成器。请将输入的内容转换为自然的双人对话形式。

要求：
1. 使用 [S1] 和 [S2] 标记两个说话人
2. 对话要自然流畅，符合日常交流习惯
3. 保持原内容的核心信息
4. 适当添加语气词和过渡语句
5. 每个说话人的话语长度适中

示例格式：
[S1]你好，今天我们来聊聊人工智能的发展。[S2]好的，这确实是个很有趣的话题。[S1]是的，最近AI技术发展得特别快..."""
        else:
            return """You are a professional dialogue script generator. Please convert the input content into natural two-person dialogue format.

Requirements:
1. Use [S1] and [S2] to mark two speakers
2. Dialogue should be natural and fluent
3. Maintain core information from original content
4. Add appropriate interjections and transitions
5. Keep each speaker's utterances at moderate length

Example format:
[S1]Hello, let's talk about AI development today.[S2]Sure, that's a really interesting topic.[S1]Yes, AI technology has been developing very rapidly recently..."""
    
    def _get_podcast_prompt(self, language: str, style: str) -> str:
        """获取播客脚本提示词"""
        if language == "zh":
            return """你是一个专业的播客脚本编写者。请将输入的内容转换为引人入胜的播客对话。

要求：
1. 使用 [S1] 和 [S2] 标记主持人和嘉宾
2. 开头要有吸引人的引入
3. 对话要有深度和互动性
4. 适当添加提问、总结和过渡
5. 结尾要有总结和展望
6. 语言要生动有趣，适合音频收听

播客风格：对话式、深入浅出、有趣味性"""
        else:
            return """You are a professional podcast script writer. Please convert the input content into engaging podcast dialogue.

Requirements:
1. Use [S1] and [S2] to mark host and guest
2. Start with an engaging introduction
3. Dialogue should be deep and interactive
4. Add appropriate questions, summaries, and transitions
5. End with conclusion and outlook
6. Language should be vivid and suitable for audio listening

Podcast style: Conversational, accessible, engaging"""
    
    def _get_long_audio_prompt(self, language: str, style: str) -> str:
        """获取长音频脚本提示词"""
        if language == "zh":
            return """你是一个专业的长音频内容编写者。请将输入的长文本转换为适合音频播放的对话形式。

要求：
1. 使用 [S1] 和 [S2] 标记两个说话人
2. 将长文本合理分段，每段转换为对话
3. 保持内容的逻辑性和连贯性
4. 适当添加解释和举例
5. 语言要口语化，易于理解
6. 控制每个对话段落的长度

适合长时间收听的音频内容"""
        else:
            return """You are a professional long-form audio content writer. Please convert the input long text into dialogue format suitable for audio playback.

Requirements:
1. Use [S1] and [S2] to mark two speakers
2. Reasonably segment long text and convert each segment to dialogue
3. Maintain logical flow and coherence
4. Add appropriate explanations and examples
5. Use conversational language that's easy to understand
6. Control the length of each dialogue segment

Suitable for long-duration audio listening"""

# 全局实例
siliconflow_adapter = SiliconFlowAdapter()
```

## 3. LangGraph 工作流实现

### 3.1 脚本生成子图

#### src/graphs/script_generation.py
```python
from langgraph import StateGraph, END
from typing import Dict, Any, TypedDict
import structlog
from src.adapters.siliconflow import siliconflow_adapter

logger = structlog.get_logger()

class ScriptGenerationState(TypedDict):
    content: str
    script_type: str
    language: str
    style: str
    generated_script: str
    error: str

def analyze_content(state: ScriptGenerationState) -> ScriptGenerationState:
    """分析内容类型和特征"""
    logger.info("Analyzing content for script generation")
    
    content = state["content"]
    
    # 简单的内容分析逻辑
    if len(content) > 5000:
        state["style"] = "detailed"
    elif "问答" in content or "Q&A" in content:
        state["style"] = "interview"
    else:
        state["style"] = "conversational"
    
    return state

async def generate_script(state: ScriptGenerationState) -> ScriptGenerationState:
    """生成脚本"""
    logger.info("Generating script", script_type=state["script_type"])
    
    try:
        script = await siliconflow_adapter.generate_script(
            content=state["content"],
            script_type=state["script_type"],
            language=state["language"],
            style=state["style"]
        )
        
        state["generated_script"] = script
        logger.info("Script generated successfully")
        
    except Exception as e:
        state["error"] = str(e)
        logger.error("Script generation failed", error=str(e))
    
    return state

def validate_script(state: ScriptGenerationState) -> ScriptGenerationState:
    """验证脚本格式"""
    logger.info("Validating generated script")
    
    script = state["generated_script"]
    
    # 检查是否包含说话人标记
    if "[S1]" not in script or "[S2]" not in script:
        state["error"] = "Generated script missing speaker tags"
        logger.warning("Script validation failed: missing speaker tags")
    else:
        logger.info("Script validation passed")
    
    return state

# 创建脚本生成子图
def create_script_generation_graph():
    workflow = StateGraph(ScriptGenerationState)
    
    # 添加节点
    workflow.add_node("analyze_content", analyze_content)
    workflow.add_node("generate_script", generate_script)
    workflow.add_node("validate_script", validate_script)
    
    # 添加边
    workflow.set_entry_point("analyze_content")
    workflow.add_edge("analyze_content", "generate_script")
    workflow.add_edge("generate_script", "validate_script")
    workflow.add_edge("validate_script", END)
    
    return workflow.compile()

# 全局图实例
script_generation_graph = create_script_generation_graph()
```

### 3.2 长音频生成图

#### src/graphs/long_audio.py
```python
from langgraph import StateGraph, END
from typing import Dict, Any, TypedDict, List
import structlog
import tempfile
import torchaudio
from src.graphs.script_generation import script_generation_graph
from src.adapters.moss_ttsd import moss_ttsd_adapter

logger = structlog.get_logger()

class LongAudioState(TypedDict):
    input_text: str
    segments: List[str]
    scripts: List[str]
    audio_segments: List[str]
    final_audio_path: str
    use_normalize: bool
    error: str

def segment_text(state: LongAudioState) -> LongAudioState:
    """文本分段"""
    logger.info("Segmenting long text")
    
    text = state["input_text"]
    
    # 简单的分段逻辑（可以优化为更智能的分段）
    max_segment_length = 2000
    segments = []
    
    sentences = text.split('。')
    current_segment = ""
    
    for sentence in sentences:
        if len(current_segment + sentence) < max_segment_length:
            current_segment += sentence + "。"
        else:
            if current_segment:
                segments.append(current_segment.strip())
            current_segment = sentence + "。"
    
    if current_segment:
        segments.append(current_segment.strip())
    
    state["segments"] = segments
    logger.info(f"Text segmented into {len(segments)} parts")
    
    return state

async def generate_scripts(state: LongAudioState) -> LongAudioState:
    """为每个段落生成脚本"""
    logger.info("Generating scripts for segments")
    
    scripts = []
    
    for i, segment in enumerate(state["segments"]):
        try:
            # 调用脚本生成子图
            script_result = await script_generation_graph.ainvoke({
                "content": segment,
                "script_type": "long_audio",
                "language": "zh",
                "style": "conversational"
            })
            
            if script_result.get("error"):
                raise Exception(f"Script generation failed for segment {i}: {script_result['error']}")
            
            scripts.append(script_result["generated_script"])
            
        except Exception as e:
            state["error"] = f"Failed to generate script for segment {i}: {str(e)}"
            logger.error("Script generation failed", segment=i, error=str(e))
            return state
    
    state["scripts"] = scripts
    logger.info(f"Generated {len(scripts)} scripts")
    
    return state

async def generate_audio_segments(state: LongAudioState) -> LongAudioState:
    """生成音频段落"""
    logger.info("Generating audio segments")
    
    audio_segments = []
    
    for i, script in enumerate(state["scripts"]):
        try:
            # 使用 MOSS-TTSD 生成音频
            audio_result = await moss_ttsd_adapter.generate_audio(
                text=script,
                use_normalize=state["use_normalize"]
            )
            
            # 保存音频段落到临时文件
            temp_file = tempfile.NamedTemporaryFile(suffix=".wav", delete=False)
            torchaudio.save(
                temp_file.name,
                audio_result["audio_data"],
                audio_result["sample_rate"]
            )
            
            audio_segments.append(temp_file.name)
            
        except Exception as e:
            state["error"] = f"Failed to generate audio for segment {i}: {str(e)}"
            logger.error("Audio generation failed", segment=i, error=str(e))
            return state
    
    state["audio_segments"] = audio_segments
    logger.info(f"Generated {len(audio_segments)} audio segments")
    
    return state

def concatenate_audio(state: LongAudioState) -> LongAudioState:
    """拼接音频"""
    logger.info("Concatenating audio segments")
    
    try:
        # 加载所有音频段落
        audio_tensors = []
        sample_rate = None
        
        for audio_path in state["audio_segments"]:
            audio, sr = torchaudio.load(audio_path)
            if sample_rate is None:
                sample_rate = sr
            elif sr != sample_rate:
                # 重采样到统一采样率
                audio = torchaudio.functional.resample(audio, sr, sample_rate)
            
            audio_tensors.append(audio)
        
        # 拼接音频
        concatenated_audio = torch.cat(audio_tensors, dim=1)
        
        # 保存最终音频
        final_audio_file = tempfile.NamedTemporaryFile(suffix=".wav", delete=False)
        torchaudio.save(final_audio_file.name, concatenated_audio, sample_rate)
        
        state["final_audio_path"] = final_audio_file.name
        logger.info("Audio concatenation completed")
        
    except Exception as e:
        state["error"] = f"Audio concatenation failed: {str(e)}"
        logger.error("Audio concatenation failed", error=str(e))
    
    return state

# 创建长音频生成图
def create_long_audio_graph():
    workflow = StateGraph(LongAudioState)
    
    # 添加节点
    workflow.add_node("segment_text", segment_text)
    workflow.add_node("generate_scripts", generate_scripts)
    workflow.add_node("generate_audio_segments", generate_audio_segments)
    workflow.add_node("concatenate_audio", concatenate_audio)
    
    # 添加边
    workflow.set_entry_point("segment_text")
    workflow.add_edge("segment_text", "generate_scripts")
    workflow.add_edge("generate_scripts", "generate_audio_segments")
    workflow.add_edge("generate_audio_segments", "concatenate_audio")
    workflow.add_edge("concatenate_audio", END)
    
    return workflow.compile()

# 全局图实例
long_audio_graph = create_long_audio_graph()
```

## 4. API 路由实现

### 4.1 音频生成路由

#### src/api/routes/audio.py
```python
from fastapi import APIRouter, HTTPException, BackgroundTasks, UploadFile, File
from typing import Optional
import structlog
from src.models.requests import (
    AudioGenerationRequest,
    VoiceCloneRequest,
    LongAudioRequest
)
from src.models.responses import TaskResponse
from src.services.task_service import task_service
from src.services.audio_service import audio_service

logger = structlog.get_logger()
router = APIRouter()

@router.post("/dialogue", response_model=TaskResponse)
async def generate_dialogue_audio(
    request: AudioGenerationRequest,
    background_tasks: BackgroundTasks
):
    """生成对话音频"""
    try:
        task_id = await task_service.create_task(
            task_type="dialogue_audio",
            input_data=request.dict()
        )
        
        # 添加后台任务
        background_tasks.add_task(
            audio_service.process_dialogue_audio,
            task_id,
            request
        )
        