{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/use-websocket.ts"], "names": ["useWebsocket", "useSendMessage", "useTurbopack", "useWebsocketPing", "assetPrefix", "webSocketRef", "useRef", "useEffect", "current", "url", "getSocketUrl", "window", "WebSocket", "sendMessage", "useCallback", "data", "socket", "readyState", "OPEN", "send", "turbopackState", "init", "queue", "callback", "undefined", "processTurbopackMessage", "msg", "type", "startsWith", "push", "initCurrent", "then", "connect", "addMessageListener", "cb", "websocketRef", "tree", "useContext", "GlobalLayoutRouterContext", "interval", "setInterval", "JSON", "stringify", "event", "appDirRoute", "clearInterval"], "mappings": ";;;;;;;;;;;;;;;;;IASgBA,YAAY;eAAZA;;IAgBAC,cAAc;eAAdA;;IAcAC,YAAY;eAAZA;;IAwDAC,gBAAgB;eAAhBA;;;uBA/F2C;+CACjB;8BACb;AAOtB,SAASH,aAAaI,WAAmB;IAC9C,MAAMC,eAAeC,IAAAA,aAAM;IAE3BC,IAAAA,gBAAS,EAAC;QACR,IAAIF,aAAaG,OAAO,EAAE;YACxB;QACF;QAEA,MAAMC,MAAMC,IAAAA,0BAAY,EAACN;QAEzBC,aAAaG,OAAO,GAAG,IAAIG,OAAOC,SAAS,CAAC,AAAC,KAAEH,MAAI;IACrD,GAAG;QAACL;KAAY;IAEhB,OAAOC;AACT;AAEO,SAASJ,eAAeI,YAA6C;IAC1E,MAAMQ,cAAcC,IAAAA,kBAAW,EAC7B,CAACC;QACC,MAAMC,SAASX,aAAaG,OAAO;QACnC,IAAI,CAACQ,UAAUA,OAAOC,UAAU,KAAKD,OAAOE,IAAI,EAAE;YAChD;QACF;QACA,OAAOF,OAAOG,IAAI,CAACJ;IACrB,GACA;QAACV;KAAa;IAEhB,OAAOQ;AACT;AAEO,SAASX,aAAaW,WAA8C;IACzE,MAAMO,iBAAiBd,IAAAA,aAAM,EAI1B;QACDe,MAAM;QACN,0FAA0F;QAC1FC,OAAO,EAAE;QACTC,UAAUC;IACZ;IAEA,MAAMC,0BAA0BX,IAAAA,kBAAW,EAAC,CAACY;YACtBA;QAArB,IAAI,UAAUA,SAAOA,YAAAA,IAAIC,IAAI,qBAARD,UAAUE,UAAU,CAAC,gBAAe;YACvD,MAAM,EAAEL,QAAQ,EAAED,KAAK,EAAE,GAAGF,eAAeZ,OAAO;YAClD,IAAIe,UAAU;gBACZA,SAASG;YACX,OAAO;gBACLJ,MAAOO,IAAI,CAACH;YACd;YACA,OAAO;QACT;QACA,OAAO;IACT,GAAG,EAAE;IAELnB,IAAAA,gBAAS,EAAC;QACR,MAAM,EAAEC,SAASsB,WAAW,EAAE,GAAGV;QACjC,2DAA2D;QAC3D,IAAIU,YAAYT,IAAI,EAAE;YACpB;QACF;QACAS,YAAYT,IAAI,GAAG;QAEnB,MAAM,CACJ,gGAAgG;QAChG,iEACAU,IAAI,CAAC;gBAAC,EAAEC,OAAO,EAAE;YACjB,MAAM,EAAExB,OAAO,EAAE,GAAGY;YACpBY,QAAQ;gBACNC,oBAAmBC,EAAmC;oBACpD1B,QAAQe,QAAQ,GAAGW;oBAEnB,iFAAiF;oBACjF,KAAK,MAAMR,OAAOlB,QAAQc,KAAK,CAAG;wBAChCY,GAAGR;oBACL;oBACAlB,QAAQc,KAAK,GAAGE;gBAClB;gBACAX;YACF;QACF;IACF,GAAG;QAACA;KAAY;IAEhB,OAAOY;AACT;AAEO,SAAStB,iBACdgC,YAA6C;IAE7C,MAAMtB,cAAcZ,eAAekC;IACnC,MAAM,EAAEC,IAAI,EAAE,GAAGC,IAAAA,iBAAU,EAACC,wDAAyB;IAErD/B,IAAAA,gBAAS,EAAC;QACR,yCAAyC;QACzC,MAAMgC,WAAWC,YAAY;YAC3B3B,YACE4B,KAAKC,SAAS,CAAC;gBACbC,OAAO;gBACPP;gBACAQ,aAAa;YACf;QAEJ,GAAG;QACH,OAAO,IAAMC,cAAcN;IAC7B,GAAG;QAACH;QAAMvB;KAAY;AACxB"}