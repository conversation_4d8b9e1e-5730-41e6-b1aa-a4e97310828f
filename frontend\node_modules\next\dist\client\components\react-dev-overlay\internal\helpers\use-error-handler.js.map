{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/use-error-handler.ts"], "names": ["RuntimeError<PERSON>andler", "useErrorHandler", "hadRuntimeError", "isHydrationError", "error", "message", "match", "window", "Error", "stackTraceLimit", "errorQueue", "rejectionQueue", "errorHandlers", "rejectionHandlers", "addEventListener", "ev", "isNextRouterError", "preventDefault", "stack", "includes", "hydrationErrorWarning", "hydrationErrorComponentStack", "_componentStack", "e", "push", "handler", "reason", "handleOnUnhandledError", "handleOnUnhandledRejection", "useEffect", "for<PERSON>ach", "splice", "indexOf"], "mappings": ";;;;;;;;;;;;;;;IASaA,mBAAmB;eAAnBA;;IA0FGC,eAAe;eAAfA;;;uBAnGU;oCAInB;mCAC2B;AAI3B,MAAMD,sBAAsB;IACjCE,iBAAiB;AACnB;AAEA,SAASC,iBAAiBC,KAAY;IACpC,OACEA,MAAMC,OAAO,CAACC,KAAK,CAAC,wDACpB;AAEJ;AAEA,IAAI,OAAOC,WAAW,aAAa;IACjC,IAAI;QACF,oDAAoD;QACpDC,MAAMC,eAAe,GAAG;IAC1B,EAAE,UAAM,CAAC;AACX;AAEA,MAAMC,aAA2B,EAAE;AACnC,MAAMC,iBAA+B,EAAE;AACvC,MAAMC,gBAAqC,EAAE;AAC7C,MAAMC,oBAAyC,EAAE;AAEjD,IAAI,OAAON,WAAW,aAAa;IACjC,6EAA6E;IAC7E,0EAA0E;IAC1E,yBAAyB;IACzBA,OAAOO,gBAAgB,CAAC,SAAS,CAACC;QAChC,IAAIC,IAAAA,oCAAiB,EAACD,GAAGX,KAAK,GAAG;YAC/BW,GAAGE,cAAc;YACjB;QACF;QAEA,MAAMb,QAAQW,sBAAAA,GAAIX,KAAK;QACvB,IACE,CAACA,SACD,CAAEA,CAAAA,iBAAiBI,KAAI,KACvB,OAAOJ,MAAMc,KAAK,KAAK,UACvB;YACA,8DAA8D;YAC9D;QACF;QAEA,IACEf,iBAAiBC,UACjB,CAACA,MAAMC,OAAO,CAACc,QAAQ,CACrB,2DAEF;YACA,IAAIC,yCAAqB,EAAE;gBACzB,mEAAmE;gBACnE,iDAAiD;gBACjDhB,MAAMC,OAAO,IAAI,SAASe,yCAAqB;YACjD;YACA,IAAIC,gDAA4B,EAAE;gBAE9BjB,MAAckB,eAAe,GAAGD,gDAA4B;YAChE;YACAjB,MAAMC,OAAO,IACX;QACJ;QAEA,MAAMkB,IAAInB;QACVM,WAAWc,IAAI,CAACD;QAChB,KAAK,MAAME,WAAWb,cAAe;YACnCa,QAAQF;QACV;IACF;IACAhB,OAAOO,gBAAgB,CACrB,sBACA,CAACC;QACC,MAAMW,SAASX,sBAAAA,GAAIW,MAAM;QACzB,IACE,CAACA,UACD,CAAEA,CAAAA,kBAAkBlB,KAAI,KACxB,OAAOkB,OAAOR,KAAK,KAAK,UACxB;YACA,8DAA8D;YAC9D;QACF;QAEA,MAAMK,IAAIG;QACVf,eAAea,IAAI,CAACD;QACpB,KAAK,MAAME,WAAWZ,kBAAmB;YACvCY,QAAQF;QACV;IACF;AAEJ;AAEO,SAAStB,gBACd0B,sBAAoC,EACpCC,0BAAwC;IAExCC,IAAAA,gBAAS,EAAC;QACR,wBAAwB;QACxBnB,WAAWoB,OAAO,CAACH;QACnBhB,eAAemB,OAAO,CAACF;QAEvB,wBAAwB;QACxBhB,cAAcY,IAAI,CAACG;QACnBd,kBAAkBW,IAAI,CAACI;QAEvB,OAAO;YACL,oBAAoB;YACpBhB,cAAcmB,MAAM,CAACnB,cAAcoB,OAAO,CAACL,yBAAyB;YACpEd,kBAAkBkB,MAAM,CACtBlB,kBAAkBmB,OAAO,CAACJ,6BAC1B;QAEJ;IACF,GAAG;QAACD;QAAwBC;KAA2B;AACzD"}