{"version": 3, "sources": ["../../../../src/build/webpack/config/helpers.ts"], "names": ["loader", "unshiftLoader", "plugin", "curry", "rule", "config", "module", "rules", "oneOf", "existing", "find", "arrayRule", "push", "unshift", "p", "plugins"], "mappings": ";;;;;;;;;;;;;;;;IAGaA,MAAM;eAANA;;IAuBAC,aAAa;eAAbA;;IAuBAC,MAAM;eAANA;;;oEAjDK;;;;;;AAGX,MAAMF,SAASG,IAAAA,oBAAK,EAAC,SAASH,OACnCI,IAAyB,EACzBC,MAA6B;QAiB7BA;IAfA,IAAI,CAACA,OAAOC,MAAM,EAAE;QAClBD,OAAOC,MAAM,GAAG;YAAEC,OAAO,EAAE;QAAC;IAC9B;IAEA,IAAIH,KAAKI,KAAK,EAAE;YACGH;QAAjB,MAAMI,YAAWJ,wBAAAA,OAAOC,MAAM,CAACC,KAAK,qBAAnBF,sBAAqBK,IAAI,CACxC,CAACC,YACCA,aAAa,OAAOA,cAAc,YAAYA,UAAUH,KAAK;QAEjE,IAAIC,YAAY,OAAOA,aAAa,UAAU;YAC5CA,SAASD,KAAK,CAAEI,IAAI,IAAIR,KAAKI,KAAK;YAClC,OAAOH;QACT;IACF;KAEAA,uBAAAA,OAAOC,MAAM,CAACC,KAAK,qBAAnBF,qBAAqBO,IAAI,CAACR;IAC1B,OAAOC;AACT;AAEO,MAAMJ,gBAAgBE,IAAAA,oBAAK,EAAC,SAASF,cAC1CG,IAAyB,EACzBC,MAA6B;QAiB7BA;IAfA,IAAI,CAACA,OAAOC,MAAM,EAAE;QAClBD,OAAOC,MAAM,GAAG;YAAEC,OAAO,EAAE;QAAC;IAC9B;IAEA,IAAIH,KAAKI,KAAK,EAAE;YACGH;QAAjB,MAAMI,YAAWJ,wBAAAA,OAAOC,MAAM,CAACC,KAAK,qBAAnBF,sBAAqBK,IAAI,CACxC,CAACC,YACCA,aAAa,OAAOA,cAAc,YAAYA,UAAUH,KAAK;QAEjE,IAAIC,YAAY,OAAOA,aAAa,UAAU;gBAC5CA;aAAAA,kBAAAA,SAASD,KAAK,qBAAdC,gBAAgBI,OAAO,IAAIT,KAAKI,KAAK;YACrC,OAAOH;QACT;IACF;KAEAA,uBAAAA,OAAOC,MAAM,CAACC,KAAK,qBAAnBF,qBAAqBQ,OAAO,CAACT;IAC7B,OAAOC;AACT;AAEO,MAAMH,SAASC,IAAAA,oBAAK,EAAC,SAASD,OACnCY,CAAgC,EAChCT,MAA6B;IAE7B,IAAI,CAACA,OAAOU,OAAO,EAAE;QACnBV,OAAOU,OAAO,GAAG,EAAE;IACrB;IACAV,OAAOU,OAAO,CAACH,IAAI,CAACE;IACpB,OAAOT;AACT"}