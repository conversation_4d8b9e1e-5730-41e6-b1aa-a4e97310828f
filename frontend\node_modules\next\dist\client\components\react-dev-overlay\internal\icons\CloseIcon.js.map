{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/icons/CloseIcon.tsx"], "names": ["CloseIcon", "svg", "width", "height", "viewBox", "fill", "xmlns", "path", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin"], "mappings": ";;;;+BA6BSA;;;eAAAA;;;;iEA7Bc;AAEvB,MAAMA,YAAY;IAChB,qBACE,qBAACC;QACCC,OAAM;QACNC,QAAO;QACPC,SAAQ;QACRC,MAAK;QACLC,OAAM;qBAEN,qBAACC;QACCC,GAAE;QACFC,QAAO;QACPC,aAAY;QACZC,eAAc;QACdC,gBAAe;sBAEjB,qBAACL;QACCC,GAAE;QACFC,QAAO;QACPC,aAAY;QACZC,eAAc;QACdC,gBAAe;;AAIvB"}