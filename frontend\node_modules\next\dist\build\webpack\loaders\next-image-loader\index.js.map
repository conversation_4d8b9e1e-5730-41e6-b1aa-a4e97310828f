{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-image-loader/index.ts"], "names": ["raw", "nextImage<PERSON><PERSON><PERSON>", "content", "imageLoaderSpan", "currentTraceSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "options", "getOptions", "compilerType", "isDev", "assetPrefix", "basePath", "context", "rootContext", "opts", "interpolatedName", "loaderUtils", "interpolateName", "outputPath", "extension", "imageSizeSpan", "imageSize", "getImageSize", "catch", "err", "Error", "name", "dataURL", "blurDataURL", "width", "blur<PERSON>idth", "height", "blurHeight", "getBlurImage", "tracing", "bind", "stringifiedData", "traceFn", "JSON", "stringify", "src", "emitFile", "path", "join"], "mappings": ";;;;;;;;;;;;;;;IAqFaA,GAAG;eAAHA;;IACb,OAA8B;eAA9B;;;6DApFiB;qEACO;gCACK;sBACA;;;;;;AAS7B,SAASC,gBAA2BC,OAAe;IACjD,MAAMC,kBAAkB,IAAI,CAACC,gBAAgB,CAACC,UAAU,CAAC;IACzD,OAAOF,gBAAgBG,YAAY,CAAC;QAClC,MAAMC,UAAmB,IAAI,CAACC,UAAU;QACxC,MAAM,EAAEC,YAAY,EAAEC,KAAK,EAAEC,WAAW,EAAEC,QAAQ,EAAE,GAAGL;QACvD,MAAMM,UAAU,IAAI,CAACC,WAAW;QAEhC,MAAMC,OAAO;YAAEF;YAASX;QAAQ;QAChC,MAAMc,mBAAmBC,qBAAW,CAACC,eAAe,CAClD,IAAI,EACJ,uCACAH;QAEF,MAAMI,aAAaR,cAAc,WAAWK;QAC5C,IAAII,YAAYH,qBAAW,CAACC,eAAe,CAAC,IAAI,EAAE,SAASH;QAC3D,IAAIK,cAAc,OAAO;YACvBA,YAAY;QACd;QAEA,MAAMC,gBAAgBlB,gBAAgBE,UAAU,CAAC;QACjD,MAAMiB,YAAY,MAAMD,cAAcf,YAAY,CAAC,IACjDiB,IAAAA,4BAAY,EAACrB,SAASkB,WAAWI,KAAK,CAAC,CAACC,MAAQA;QAGlD,IAAIH,qBAAqBI,OAAO;YAC9B,MAAMD,MAAMH;YACZG,IAAIE,IAAI,GAAG;YACX,MAAMF;QACR;QAEA,MAAM,EACJG,SAASC,WAAW,EACpBC,OAAOC,SAAS,EAChBC,QAAQC,UAAU,EACnB,GAAG,MAAMC,IAAAA,kBAAY,EAAChC,SAASkB,WAAWE,WAAW;YACpDV;YACAO;YACAT;YACAyB,SAAShC,gBAAgBE,UAAU,CAAC+B,IAAI,CAACjC;QAC3C;QAEA,MAAMkC,kBAAkBlC,gBACrBE,UAAU,CAAC,wBACXiC,OAAO,CAAC,IACPC,KAAKC,SAAS,CAAC;gBACbC,KAAKtB;gBACLa,QAAQV,UAAUU,MAAM;gBACxBF,OAAOR,UAAUQ,KAAK;gBACtBD;gBACAE;gBACAE;YACF;QAGJ,IAAIxB,iBAAiB,UAAU;YAC7B,IAAI,CAACiC,QAAQ,CAAC1B,kBAAkBd,SAAS;QAC3C,OAAO;YACL,IAAI,CAACwC,QAAQ,CACXC,aAAI,CAACC,IAAI,CACP,MACAlC,SAASD,iBAAiB,gBAAgB,KAAK,MAC/CO,mBAEFd,SACA;QAEJ;QAEA,OAAO,CAAC,eAAe,EAAEmC,gBAAgB,CAAC,CAAC;IAC7C;AACF;AACO,MAAMrC,MAAM;MACnB,WAAeC"}