# Wuzhen-MOSS-TTSD 部署指南

## 1. 环境准备

### 1.1 系统要求

```yaml
操作系统: Ubuntu 20.04+ / CentOS 8+ / macOS 12+
Python: 3.10+
GPU: NVIDIA GPU with CUDA 11.8+ (推荐)
内存: 16GB+ (32GB 推荐)
存储: 100GB+ SSD
```

### 1.2 依赖安装

#### Docker 环境 (推荐)
```bash
# 安装 Docker 和 Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 安装 NVIDIA Container Toolkit (GPU 支持)
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list
sudo apt-get update && sudo apt-get install -y nvidia-docker2
sudo systemctl restart docker
```

#### 本地环境
```bash
# Python 环境
pyenv install 3.10.12
pyenv global 3.10.12

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
pip install flash-attn --no-build-isolation
```

## 2. 配置文件

### 2.1 环境变量配置

#### .env
```bash
# 基础配置
DEBUG=false
SECRET_KEY=your-super-secret-key-change-this-in-production
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# 数据库配置
DATABASE_URL=postgresql://wuzhen:password@localhost:5432/wuzhen_moss
REDIS_URL=redis://localhost:6379/0

# MOSS-TTSD 配置
MOSS_TTSD_MODEL_PATH=fnlp/MOSS-TTSD-v0.5
MOSS_TTSD_SPT_CONFIG=moss_ttsd/XY_Tokenizer/config/xy_tokenizer_config.yaml
MOSS_TTSD_SPT_CHECKPOINT=moss_ttsd/XY_Tokenizer/weights/xy_tokenizer.ckpt

# 硅基流动配置
SILICONFLOW_API_KEY=your-siliconflow-api-key
SILICONFLOW_API_BASE=https://api.siliconflow.cn/v1

# 文件存储配置
UPLOAD_DIR=/app/uploads
OUTPUT_DIR=/app/outputs
MAX_FILE_SIZE=104857600  # 100MB

# 任务配置
MAX_CONCURRENT_TASKS=10
TASK_TIMEOUT=3600

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090
```

### 2.2 Docker Compose 配置

#### docker-compose.yml
```yaml
version: '3.8'

services:
  # 主应用服务
  api:
    build:
      context: .
      dockerfile: docker/Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=************************************/wuzhen_moss
      - REDIS_URL=redis://redis:6379/0
    env_file:
      - .env
    volumes:
      - ./uploads:/app/uploads
      - ./outputs:/app/outputs
      - ./moss_ttsd:/app/moss_ttsd
    depends_on:
      - db
      - redis
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  # 异步任务处理器
  worker:
    build:
      context: .
      dockerfile: docker/Dockerfile
    command: celery -A src.core.celery worker --loglevel=info --concurrency=4
    environment:
      - DATABASE_URL=************************************/wuzhen_moss
      - REDIS_URL=redis://redis:6379/0
    env_file:
      - .env
    volumes:
      - ./uploads:/app/uploads
      - ./outputs:/app/outputs
      - ./moss_ttsd:/app/moss_ttsd
    depends_on:
      - db
      - redis
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  # 任务监控
  flower:
    build:
      context: .
      dockerfile: docker/Dockerfile
    command: celery -A src.core.celery flower --port=5555
    ports:
      - "5555:5555"
    environment:
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - redis
    restart: unless-stopped

  # 数据库
  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=wuzhen_moss
      - POSTGRES_USER=wuzhen
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped

  # Redis 缓存
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped

  # 反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/ssl:/etc/nginx/ssl
    depends_on:
      - api
    restart: unless-stopped

  # 监控
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./docker/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:
```

### 2.3 Dockerfile

#### docker/Dockerfile
```dockerfile
FROM nvidia/cuda:11.8-devel-ubuntu20.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    python3.10 \
    python3.10-dev \
    python3-pip \
    git \
    wget \
    curl \
    ffmpeg \
    libsndfile1 \
    && rm -rf /var/lib/apt/lists/*

# 设置 Python 别名
RUN ln -s /usr/bin/python3.10 /usr/bin/python

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt .

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install --no-cache-dir flash-attn --no-build-isolation

# 复制应用代码
COPY src/ ./src/
COPY config/ ./config/
COPY scripts/ ./scripts/

# 创建必要的目录
RUN mkdir -p /app/uploads /app/outputs /app/logs

# 设置权限
RUN chmod +x /app/scripts/*.sh

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 2.4 Nginx 配置

#### docker/nginx.conf
```nginx
events {
    worker_connections 1024;
}

http {
    upstream api {
        server api:8000;
    }

    # 限制请求大小
    client_max_body_size 100M;

    # 启用 gzip 压缩
    gzip on;
    gzip_types text/plain application/json application/javascript text/css;

    # API 服务
    server {
        listen 80;
        server_name localhost;

        # API 路由
        location /api/ {
            proxy_pass http://api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket 支持
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            
            # 超时设置
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # 健康检查
        location /health {
            proxy_pass http://api/health;
        }

        # 静态文件
        location /static/ {
            alias /app/static/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # 文件下载
        location /downloads/ {
            alias /app/outputs/;
            add_header Content-Disposition "attachment";
        }
    }

    # HTTPS 配置 (生产环境)
    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

        location / {
            proxy_pass http://api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
```

## 3. 部署步骤

### 3.1 开发环境部署

```bash
# 1. 克隆项目
git clone <repository-url>
cd wuzhen-moss-ttsd

# 2. 设置 MOSS-TTSD 子模块
git submodule add <moss-ttsd-repo-url> moss_ttsd
git submodule update --init --recursive

# 3. 下载 XY Tokenizer 权重
mkdir -p moss_ttsd/XY_Tokenizer/weights
huggingface-cli download fnlp/XY_Tokenizer_TTSD_V0 xy_tokenizer.ckpt --local-dir ./moss_ttsd/XY_Tokenizer/weights/

# 4. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，填入正确的配置

# 5. 启动数据库服务
docker-compose up -d db redis

# 6. 安装依赖
pip install -r requirements.txt
pip install flash-attn --no-build-isolation

# 7. 数据库迁移
python scripts/migrate.py

# 8. 启动应用
uvicorn src.main:app --reload --host 0.0.0.0 --port 8000

# 9. 启动 Worker (新终端)
celery -A src.core.celery worker --loglevel=info
```

### 3.2 生产环境部署

```bash
# 1. 服务器准备
sudo apt update && sudo apt upgrade -y
sudo apt install -y docker.io docker-compose-plugin

# 2. 克隆项目
git clone <repository-url>
cd wuzhen-moss-ttsd

# 3. 配置生产环境
cp .env.example .env
# 编辑生产环境配置

# 4. 构建和启动服务
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# 5. 数据库初始化
docker-compose exec api python scripts/migrate.py

# 6. 验证部署
curl http://localhost/health
```

### 3.3 Kubernetes 部署

#### k8s/namespace.yaml
```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: wuzhen-moss-ttsd
```

#### k8s/configmap.yaml
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: wuzhen-moss-ttsd-config
  namespace: wuzhen-moss-ttsd
data:
  DATABASE_URL: "******************************************/wuzhen_moss"
  REDIS_URL: "redis://redis:6379/0"
  MOSS_TTSD_MODEL_PATH: "fnlp/MOSS-TTSD-v0.5"
  MAX_CONCURRENT_TASKS: "10"
  TASK_TIMEOUT: "3600"
```

#### k8s/deployment.yaml
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: wuzhen-moss-ttsd-api
  namespace: wuzhen-moss-ttsd
spec:
  replicas: 3
  selector:
    matchLabels:
      app: wuzhen-moss-ttsd-api
  template:
    metadata:
      labels:
        app: wuzhen-moss-ttsd-api
    spec:
      containers:
      - name: api
        image: wuzhen-moss-ttsd:latest
        ports:
        - containerPort: 8000
        envFrom:
        - configMapRef:
            name: wuzhen-moss-ttsd-config
        - secretRef:
            name: wuzhen-moss-ttsd-secrets
        resources:
          requests:
            memory: "4Gi"
            cpu: "1000m"
            nvidia.com/gpu: 1
          limits:
            memory: "8Gi"
            cpu: "2000m"
            nvidia.com/gpu: 1
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
```

#### k8s/service.yaml
```yaml
apiVersion: v1
kind: Service
metadata:
  name: wuzhen-moss-ttsd-service
  namespace: wuzhen-moss-ttsd
spec:
  selector:
    app: wuzhen-moss-ttsd-api
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8000
  type: LoadBalancer
```

## 4. 监控和日志

### 4.1 Prometheus 配置

#### docker/prometheus.yml
```yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'wuzhen-moss-ttsd'
    static_configs:
      - targets: ['api:8000']
    metrics_path: '/metrics'
    scrape_interval: 5s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  - job_name: 'postgres'
    static_configs:
      - targets: ['db:5432']
```

### 4.2 Grafana 仪表板

#### docker/grafana/dashboards/dashboard.json
```json
{
  "dashboard": {
    "title": "Wuzhen-MOSS-TTSD Monitoring",
    "panels": [
      {
        "title": "API Requests",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{method}} {{endpoint}}"
          }
        ]
      },
      {
        "title": "Task Queue Length",
        "type": "singlestat",
        "targets": [
          {
            "expr": "celery_queue_length",
            "legendFormat": "Queue Length"
          }
        ]
      },
      {
        "title": "GPU Utilization",
        "type": "graph",
        "targets": [
          {
            "expr": "nvidia_gpu_utilization_gpu",
            "legendFormat": "GPU {{gpu}}"
          }
        ]
      }
    ]
  }
}
```

### 4.3 日志配置

#### config/logging.yaml
```yaml
version: 1
disable_existing_loggers: false

formatters:
  standard:
    format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
  json:
    format: "%(asctime)s %(name)s %(levelname)s %(message)s"
    class: pythonjsonlogger.jsonlogger.JsonFormatter

handlers:
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: standard
    stream: ext://sys.stdout

  file:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: json
    filename: /app/logs/app.log
    maxBytes: 10485760  # 10MB
    backupCount: 5

  error_file:
    class: logging.handlers.RotatingFileHandler
    level: ERROR
    formatter: json
    filename: /app/logs/error.log
    maxBytes: 10485760  # 10MB
    backupCount: 5

loggers:
  src:
    level: INFO
    handlers: [console, file]
    propagate: false

  uvicorn:
    level: INFO
    handlers: [console, file]
    propagate: false

  celery:
    level: INFO
    handlers: [console, file]
    propagate: false

root:
  level: INFO
  handlers: [console, file, error_file]
```

## 5. 备份和恢复

### 5.1 数据库备份

#### scripts/backup.sh
```bash
#!/bin/bash

# 配置
BACKUP_DIR="/app/backups"
DB_NAME="wuzhen_moss"
DB_USER="wuzhen"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# 创建备份目录
mkdir -p $BACKUP_DIR

# 数据库备份
docker-compose exec -T db pg_dump -U $DB_USER $DB_NAME > $BACKUP_DIR/db_backup_$TIMESTAMP.sql

# 压缩备份
gzip $BACKUP_DIR/db_backup_$TIMESTAMP.sql

# 清理旧备份 (保留7天)
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +7 -delete

echo "Database backup completed: db_backup_$TIMESTAMP.sql.gz"
```

### 5.2 文件备份

#### scripts/backup_files.sh
```bash
#!/bin/bash

# 配置
BACKUP_DIR="/app/backups"
UPLOAD_DIR="/app/uploads"
OUTPUT_DIR="/app/outputs"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份上传文件
tar -czf $BACKUP_DIR/uploads_backup_$TIMESTAMP.tar.gz -C $UPLOAD_DIR .

# 备份输出文件
tar -czf $BACKUP_DIR/outputs_backup_$TIMESTAMP.tar.gz -C $OUTPUT_DIR .

# 清理旧备份
find $BACKUP_DIR -name "*_backup_*.tar.gz" -mtime +7 -delete

echo "File backup completed"
```

### 5.3 恢复脚本

#### scripts/restore.sh
```bash
#!/bin/bash

if [ $# -ne 1 ]; then
    echo "Usage: $0 <backup_timestamp>"
    exit 1
fi

TIMESTAMP=$1
BACKUP_DIR="/app/backups"
DB_NAME="wuzhen_moss"
DB_USER="wuzhen"

# 恢复数据库
if [ -f "$BACKUP_DIR/db_backup_$TIMESTAMP.sql.gz" ]; then
    echo "Restoring database..."
    gunzip -c $BACKUP_DIR/db_backup_$TIMESTAMP.sql.gz | docker-compose exec -T db psql -U $DB_USER $DB_NAME
    echo "Database restored"
else
    echo "Database backup file not found"
fi

# 恢复文件
if [ -f "$BACKUP_DIR/uploads_backup_$TIMESTAMP.tar.gz" ]; then
    echo "Restoring upload files..."
    tar -xzf $BACKUP_DIR/uploads_backup_$TIMESTAMP.tar.gz -C /app/uploads/
    echo "Upload files restored"
fi

if [ -f "$BACKUP_DIR/outputs_backup_$TIMESTAMP.tar.gz" ]; then
    echo "Restoring output files..."
    tar -xzf $BACKUP_DIR/outputs_backup_$TIMESTAMP.tar.gz -C /app/outputs/
    echo "Output files restored"
fi

echo "Restore completed"
```

## 6. 性能优化

### 6.1 数据库优化

#### scripts/optimize_db.sql
```sql
-- 创建索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tasks_status ON tasks(status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tasks_created_at ON tasks(created_at);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tasks_type ON tasks(type);

-- 配置优化
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;

-- 重新加载配置
SELECT pg_reload_conf();
```

### 6.2 Redis 优化

#### docker/redis.conf
```conf
# 内存优化
maxmemory 512mb
maxmemory-policy allkeys-lru

# 持久化优化
save 900 1
save 300 10
save 60 10000

# 网络优化
tcp-keepalive 300
timeout 0

# 日志
loglevel notice
logfile /var/log/redis/redis-server.log
```

### 6.3 应用优化

#### src/core/performance.py
```python
import asyncio
from functools import wraps
import time
import structlog

logger = structlog.get_logger()

def async_cache(ttl: int = 300):
    """异步缓存装饰器"""
    cache = {}
    
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            if key in cache:
                result, timestamp = cache[key]
                if time.time() - timestamp < ttl:
                    return result
            
            result = await func(*args, **kwargs)
            cache[key] = (result, time.time())
            
            return result
        return wrapper
    return decorator

def rate_limit(calls: int, period: int):
    """速率限制装饰器"""
    call_times = []
    
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            now = time.time()
            
            # 清理过期的调用记录
            call_times[:] = [t for t in call_times if now - t < period]
            
            if len(call_times) >= calls:
                raise Exception(f"Rate limit exceeded: {calls} calls per {period} seconds")
            
            call_times.append(now)
            return await func(*args, **kwargs)
        return wrapper
    return decorator
```

## 7. 安全配置

### 7.1 SSL/TLS 配置

#### scripts/generate_ssl.sh
```bash
#!/bin/bash

# 生成自签名证书 (开发环境)
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout docker/ssl/key.pem \
    -out docker/ssl/cert.pem \
    -subj "/C=CN/ST=Beijing/L=Beijing/O=Wuzhen/CN=localhost"

echo "SSL certificates generated"
```

### 7.2 防火墙配置

#### scripts/setup_firewall.sh
```bash
#!/bin/bash

# 启用 UFW
sudo ufw enable

# 允许 SSH
sudo ufw allow ssh

# 允许 HTTP/HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# 允许应用端口 (仅内网)
sudo ufw allow from 10.0.0.0/8 to any port 8000
sudo ufw allow from **********/12 to any port 8000
sudo ufw allow from ***********/16 to any port 8000

# 拒绝其他连接
sudo ufw default deny incoming
sudo ufw default allow outgoing

echo "Firewall configured"
```

## 8. 故障排除

### 8.1 常见问题

#### GPU 相关问题
```bash
# 检查 GPU 状态
nvidia-smi

# 检查 CUDA 版本
nvcc --version

# 检查 Docker GPU 支持
docker run --rm --gpus all nvidia/cuda:11.8-base-ubuntu20.04 nvidia-smi
```

#### 内存问题
```bash
# 检查内存使用
free -h
docker stats

# 清理 Docker 缓存
docker system prune -a
```

#### 网络问题
```bash
# 检查端口占用
netstat -tulpn | grep :8000

# 检查 Docker 网络
docker network ls
docker network inspect bridge
```

### 8.2 日志分析

#### scripts/analyze_logs.sh
```bash
#!/bin/bash

LOG_FILE="/app/logs/app.log"

echo "=== Error Summary ==="
grep -i error $LOG_FILE | tail -20

echo "=== Performance Issues ==="
grep -i "slow\|timeout\|memory" $LOG_FILE | tail -10

echo "=== Recent Activity ==="
tail -50 $LOG_FILE
```

## 9. 维护任务

### 9.1 定期维护脚本

#### scripts/maintenance.sh
```bash
#!/bin/bash

echo "Starting maintenance tasks..."

# 清理临时文件
find /app/temp -type f -mtime +1 -delete

# 清理日志文件
find /app/logs -name "*.log" -mtime +30 -delete

# 数据库维护
docker-compose exec db psql -U wuzhen -d wuzhen_moss -c "VACUUM ANALYZE;"

# 重启服务 (如果需要)
# docker-compose restart worker

echo "Maintenance completed"
```

### 9.2 健康检查

#### scripts/health_check.sh
```bash
#!/bin/bash

# 检查服务状态
if ! curl -f http://localhost/health > /dev/null 2>&1; then
    echo "API service is down"
    exit 1
fi

# 检查数据库连接
if ! docker-compose exec db pg_isready -U wuzhen > /dev/null 2>&1; then
    echo "Database is not ready"
    exit 1
fi

# 检查 Redis 连接
if ! docker-compose exec redis redis-cli ping > /dev/null 2>&1; then
    echo "Redis is not responding"
    exit 1
fi

echo "All services are healthy"
```

这个部署指南提供了从开发环境到生产环境的完整部署方案，包括 Docker、Kubernetes、监控、备份、安全和维护等各个方面的配置。