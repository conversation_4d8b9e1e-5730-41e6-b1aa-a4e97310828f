{"version": 3, "sources": ["../../../../src/build/babel/plugins/next-ssg-transform.ts"], "names": ["EXPORT_NAME_GET_STATIC_PROPS", "EXPORT_NAME_GET_STATIC_PATHS", "EXPORT_NAME_GET_SERVER_PROPS", "nextTransformSsg", "ssgExports", "Set", "decorateSsgExport", "t", "path", "state", "gsspName", "<PERSON><PERSON><PERSON><PERSON>", "STATIC_PROPS_ID", "SERVER_PROPS_ID", "gsspId", "identifier", "addGsspExport", "exportPath", "done", "pageCompPath", "replaceWithMultiple", "exportNamedDeclaration", "variableDeclaration", "variableDeclarator", "booleanLiteral", "exportSpecifier", "node", "scope", "registerDeclaration", "traverse", "ExportDefaultDeclaration", "exportDefaultPath", "ExportNamedDeclaration", "exportNamedPath", "isDataIdentifier", "name", "has", "Error", "SERVER_PROPS_SSG_CONFLICT", "isServerProps", "types", "getIdentifier", "parentPath", "type", "pp", "get", "id", "isIdentifierReferenced", "ident", "b", "getBinding", "referenced", "constantViolations", "concat", "referencePaths", "every", "ref", "findParent", "p", "markFunction", "refs", "add", "markImport", "local", "visitor", "Program", "enter", "VariableDeclarator", "variablePath", "variableState", "pattern", "properties", "for<PERSON>ach", "elements", "e", "FunctionDeclaration", "FunctionExpression", "ArrowFunctionExpression", "ImportSpecifier", "ImportDefaultSpecifier", "ImportNamespaceSpecifier", "exportNamedState", "specifiers", "length", "s", "isIdentifier", "exported", "value", "remove", "decl", "inner", "d", "count", "sweepFunction", "sweepPath", "isAssignmentExpression", "isVariableDeclarator", "sweepImport", "parent", "crawl", "beforeCount"], "mappings": ";;;;;;;;;;;;;;;;;IAQaA,4BAA4B;eAA5BA;;IACAC,4BAA4B;eAA5BA;;IACAC,4BAA4B;eAA5BA;;IAsFb,OAsWC;eAtWuBC;;;2BA3FkB;4BACO;AAE1C,MAAMH,+BAA+B;AACrC,MAAMC,+BAA+B;AACrC,MAAMC,+BAA+B;AAE5C,MAAME,aAAa,IAAIC,IAAI;IACzBL;IACAC;IACAC;IAEA,4DAA4D;IAC5D,2BAA2B;IAC3B,CAAC,uBAAuB,CAAC;IACzB,CAAC,uBAAuB,CAAC;IACzB,CAAC,uBAAuB,CAAC;IACzB,CAAC,2BAA2B,CAAC;CAC9B;AASD,SAASI,kBACPC,CAAoB,EACpBC,IAAkC,EAClCC,KAAkB;IAElB,MAAMC,WAAWD,MAAME,WAAW,GAAGC,2BAAe,GAAGC,2BAAe;IACtE,MAAMC,SAASP,EAAEQ,UAAU,CAACL;IAE5B,MAAMM,gBAAgB,CACpBC;QAIA,IAAIR,MAAMS,IAAI,EAAE;YACd;QACF;QACAT,MAAMS,IAAI,GAAG;QAEb,MAAM,CAACC,aAAa,GAAGF,WAAWG,mBAAmB,CAAC;YACpDb,EAAEc,sBAAsB,CACtBd,EAAEe,mBAAmB,CACnB,kEAAkE;YAClE,iEAAiE;YACjE,iCAAiC;YACjC,OACA;gBAACf,EAAEgB,kBAAkB,CAACT,QAAQP,EAAEiB,cAAc,CAAC;aAAO,GAExD;gBAACjB,EAAEkB,eAAe,CAACX,QAAQA;aAAQ;YAErCG,WAAWS,IAAI;SAChB;QACDT,WAAWU,KAAK,CAACC,mBAAmB,CAClCT;IAEJ;IAEAX,KAAKqB,QAAQ,CAAC;QACZC,0BAAyBC,iBAAiB;YACxCf,cAAce;QAChB;QACAC,wBAAuBC,eAAe;YACpCjB,cAAciB;QAChB;IACF;AACF;AAEA,MAAMC,mBAAmB,CAACC,MAAc1B;IACtC,IAAIL,WAAWgC,GAAG,CAACD,OAAO;QACxB,IAAIA,SAASjC,8BAA8B;YACzC,IAAIO,MAAME,WAAW,EAAE;gBACrB,MAAM,IAAI0B,MAAMC,oCAAyB;YAC3C;YACA7B,MAAM8B,aAAa,GAAG;QACxB,OAAO;YACL,IAAI9B,MAAM8B,aAAa,EAAE;gBACvB,MAAM,IAAIF,MAAMC,oCAAyB;YAC3C;YACA7B,MAAME,WAAW,GAAG;QACtB;QACA,OAAO;IACT;IACA,OAAO;AACT;AAEe,SAASR,iBAAiB,EACvCqC,OAAOjC,CAAC,EAGT;IACC,SAASkC,cACPjC,IAGgD;QAEhD,MAAMkC,aAAalC,KAAKkC,UAAU;QAClC,IAAIA,WAAWC,IAAI,KAAK,sBAAsB;YAC5C,MAAMC,KAAKF;YACX,MAAMP,OAAOS,GAAGC,GAAG,CAAC;YACpB,OAAOV,KAAKT,IAAI,CAACiB,IAAI,KAAK,eACrBR,OACD;QACN;QAEA,IAAIO,WAAWC,IAAI,KAAK,wBAAwB;YAC9C,MAAMC,KAAKF;YACX,MAAMP,OAAOS,GAAGC,GAAG,CAAC;YACpB,OAAOV,KAAKT,IAAI,CAACiB,IAAI,KAAK,eACrBR,OACD;QACN;QAEA,IAAI3B,KAAKkB,IAAI,CAACiB,IAAI,KAAK,2BAA2B;YAChD,OAAO;QACT;QAEA,OAAOnC,KAAKkB,IAAI,CAACoB,EAAE,IAAItC,KAAKkB,IAAI,CAACoB,EAAE,CAACH,IAAI,KAAK,eACxCnC,KAAKqC,GAAG,CAAC,QACV;IACN;IAEA,SAASE,uBACPC,KAAsC;QAEtC,MAAMC,IAAID,MAAMrB,KAAK,CAACuB,UAAU,CAACF,MAAMtB,IAAI,CAACS,IAAI;QAChD,IAAIc,qBAAAA,EAAGE,UAAU,EAAE;YACjB,uEAAuE;YACvE,6CAA6C;YAC7C,IAAIF,EAAEzC,IAAI,CAACmC,IAAI,KAAK,uBAAuB;gBACzC,OAAO,CAACM,EAAEG,kBAAkB,CACzBC,MAAM,CAACJ,EAAEK,cAAc,CACxB,+DAA+D;iBAC9DC,KAAK,CAAC,CAACC,MAAQA,IAAIC,UAAU,CAAC,CAACC,IAAMA,MAAMT,EAAEzC,IAAI;YACtD;YAEA,OAAO;QACT;QACA,OAAO;IACT;IAEA,SAASmD,aACPnD,IAGgD,EAChDC,KAAkB;QAElB,MAAMuC,QAAQP,cAAcjC;QAC5B,IAAIwC,CAAAA,yBAAAA,MAAOtB,IAAI,KAAIqB,uBAAuBC,QAAQ;YAChDvC,MAAMmD,IAAI,CAACC,GAAG,CAACb;QACjB;IACF;IAEA,SAASc,WACPtD,IAGiD,EACjDC,KAAkB;QAElB,MAAMsD,QAAQvD,KAAKqC,GAAG,CAAC;QACvB,IAAIE,uBAAuBgB,QAAQ;YACjCtD,MAAMmD,IAAI,CAACC,GAAG,CAACE;QACjB;IACF;IAEA,OAAO;QACLC,SAAS;YACPC,SAAS;gBACPC,OAAM1D,IAAI,EAAEC,KAAK;oBACfA,MAAMmD,IAAI,GAAG,IAAIvD;oBACjBI,MAAME,WAAW,GAAG;oBACpBF,MAAM8B,aAAa,GAAG;oBACtB9B,MAAMS,IAAI,GAAG;oBAEbV,KAAKqB,QAAQ,CACX;wBACEsC,oBAAmBC,YAAY,EAAEC,aAAa;4BAC5C,IAAID,aAAa1C,IAAI,CAACoB,EAAE,CAACH,IAAI,KAAK,cAAc;gCAC9C,MAAMoB,QAAQK,aAAavB,GAAG,CAC5B;gCAEF,IAAIE,uBAAuBgB,QAAQ;oCACjCM,cAAcT,IAAI,CAACC,GAAG,CAACE;gCACzB;4BACF,OAAO,IAAIK,aAAa1C,IAAI,CAACoB,EAAE,CAACH,IAAI,KAAK,iBAAiB;gCACxD,MAAM2B,UAAUF,aAAavB,GAAG,CAC9B;gCAGF,MAAM0B,aAAaD,QAAQzB,GAAG,CAAC;gCAC/B0B,WAAWC,OAAO,CAAC,CAACd;oCAClB,MAAMK,QAAQL,EAAEb,GAAG,CACjBa,EAAEhC,IAAI,CAACiB,IAAI,KAAK,mBACZ,UACAe,EAAEhC,IAAI,CAACiB,IAAI,KAAK,gBAChB,aACA,AAAC;wCACC,MAAM,IAAIN,MAAM;oCAClB;oCAEN,IAAIU,uBAAuBgB,QAAQ;wCACjCM,cAAcT,IAAI,CAACC,GAAG,CAACE;oCACzB;gCACF;4BACF,OAAO,IAAIK,aAAa1C,IAAI,CAACoB,EAAE,CAACH,IAAI,KAAK,gBAAgB;gCACvD,MAAM2B,UAAUF,aAAavB,GAAG,CAC9B;gCAGF,MAAM4B,WAAWH,QAAQzB,GAAG,CAAC;gCAC7B4B,SAASD,OAAO,CAAC,CAACE;wCAEZA,SAEOA;oCAHX,IAAIX;oCACJ,IAAIW,EAAAA,UAAAA,EAAEhD,IAAI,qBAANgD,QAAQ/B,IAAI,MAAK,cAAc;wCACjCoB,QAAQW;oCACV,OAAO,IAAIA,EAAAA,WAAAA,EAAEhD,IAAI,qBAANgD,SAAQ/B,IAAI,MAAK,eAAe;wCACzCoB,QAAQW,EAAE7B,GAAG,CACX;oCAEJ,OAAO;wCACL;oCACF;oCAEA,IAAIE,uBAAuBgB,QAAQ;wCACjCM,cAAcT,IAAI,CAACC,GAAG,CAACE;oCACzB;gCACF;4BACF;wBACF;wBACAY,qBAAqBhB;wBACrBiB,oBAAoBjB;wBACpBkB,yBAAyBlB;wBACzBmB,iBAAiBhB;wBACjBiB,wBAAwBjB;wBACxBkB,0BAA0BlB;wBAC1B9B,wBAAuBC,eAAe,EAAEgD,gBAAgB;4BACtD,MAAMC,aAAajD,gBAAgBY,GAAG,CAAC;4BACvC,IAAIqC,WAAWC,MAAM,EAAE;gCACrBD,WAAWV,OAAO,CAAC,CAACY;oCAClB,IACElD,iBACE3B,EAAE8E,YAAY,CAACD,EAAE1D,IAAI,CAAC4D,QAAQ,IAC1BF,EAAE1D,IAAI,CAAC4D,QAAQ,CAACnD,IAAI,GACpBiD,EAAE1D,IAAI,CAAC4D,QAAQ,CAACC,KAAK,EACzBN,mBAEF;wCACAG,EAAEI,MAAM;oCACV;gCACF;gCAEA,IAAIvD,gBAAgBP,IAAI,CAACwD,UAAU,CAACC,MAAM,GAAG,GAAG;oCAC9ClD,gBAAgBuD,MAAM;gCACxB;gCACA;4BACF;4BAEA,MAAMC,OAAOxD,gBAAgBY,GAAG,CAAC;4BAIjC,IAAI4C,QAAQ,QAAQA,KAAK/D,IAAI,IAAI,MAAM;gCACrC;4BACF;4BAEA,OAAQ+D,KAAK/D,IAAI,CAACiB,IAAI;gCACpB,KAAK;oCAAuB;wCAC1B,MAAMR,OAAOsD,KAAK/D,IAAI,CAACoB,EAAE,CAAEX,IAAI;wCAC/B,IAAID,iBAAiBC,MAAM8C,mBAAmB;4CAC5ChD,gBAAgBuD,MAAM;wCACxB;wCACA;oCACF;gCACA,KAAK;oCAAuB;wCAC1B,MAAME,QAAQD,KAAK5C,GAAG,CACpB;wCAEF6C,MAAMlB,OAAO,CAAC,CAACmB;4CACb,IAAIA,EAAEjE,IAAI,CAACoB,EAAE,CAACH,IAAI,KAAK,cAAc;gDACnC;4CACF;4CACA,MAAMR,OAAOwD,EAAEjE,IAAI,CAACoB,EAAE,CAACX,IAAI;4CAC3B,IAAID,iBAAiBC,MAAM8C,mBAAmB;gDAC5CU,EAAEH,MAAM;4CACV;wCACF;wCACA;oCACF;gCACA;oCAAS;wCACP;oCACF;4BACF;wBACF;oBACF,GACA/E;oBAGF,IAAI,CAACA,MAAME,WAAW,IAAI,CAACF,MAAM8B,aAAa,EAAE;wBAC9C;oBACF;oBAEA,MAAMqB,OAAOnD,MAAMmD,IAAI;oBACvB,IAAIgC;oBAEJ,SAASC,cACPC,SAGgD;wBAEhD,MAAM9C,QAAQP,cAAcqD;wBAC5B,IACE9C,CAAAA,yBAAAA,MAAOtB,IAAI,KACXkC,KAAKxB,GAAG,CAACY,UACT,CAACD,uBAAuBC,QACxB;4BACA,EAAE4C;4BAEF,IACErF,EAAEwF,sBAAsB,CAACD,UAAUpD,UAAU,CAAChB,IAAI,KAClDnB,EAAEyF,oBAAoB,CAACF,UAAUpD,UAAU,CAAChB,IAAI,GAChD;gCACAoE,UAAUpD,UAAU,CAAC8C,MAAM;4BAC7B,OAAO;gCACLM,UAAUN,MAAM;4BAClB;wBACF;oBACF;oBAEA,SAASS,YACPH,SAGiD;wBAEjD,MAAM/B,QAAQ+B,UAAUjD,GAAG,CACzB;wBAEF,IAAIe,KAAKxB,GAAG,CAAC2B,UAAU,CAAChB,uBAAuBgB,QAAQ;4BACrD,EAAE6B;4BACFE,UAAUN,MAAM;4BAChB,IACE,AAACM,UAAUI,MAAM,CAAkChB,UAAU,CAC1DC,MAAM,KAAK,GACd;gCACAW,UAAUpD,UAAU,CAAC8C,MAAM;4BAC7B;wBACF;oBACF;oBAEA,GAAG;wBACChF,KAAKmB,KAAK,CAASwE,KAAK;wBAC1BP,QAAQ;wBAERpF,KAAKqB,QAAQ,CAAC;4BACZ,wCAAwC;4BACxCsC,oBAAmBC,YAAY;gCAC7B,IAAIA,aAAa1C,IAAI,CAACoB,EAAE,CAACH,IAAI,KAAK,cAAc;oCAC9C,MAAMoB,QAAQK,aAAavB,GAAG,CAC5B;oCAEF,IAAIe,KAAKxB,GAAG,CAAC2B,UAAU,CAAChB,uBAAuBgB,QAAQ;wCACrD,EAAE6B;wCACFxB,aAAaoB,MAAM;oCACrB;gCACF,OAAO,IAAIpB,aAAa1C,IAAI,CAACoB,EAAE,CAACH,IAAI,KAAK,iBAAiB;oCACxD,MAAM2B,UAAUF,aAAavB,GAAG,CAC9B;oCAGF,MAAMuD,cAAcR;oCACpB,MAAMrB,aAAaD,QAAQzB,GAAG,CAAC;oCAC/B0B,WAAWC,OAAO,CAAC,CAACd;wCAClB,MAAMK,QAAQL,EAAEb,GAAG,CACjBa,EAAEhC,IAAI,CAACiB,IAAI,KAAK,mBACZ,UACAe,EAAEhC,IAAI,CAACiB,IAAI,KAAK,gBAChB,aACA,AAAC;4CACC,MAAM,IAAIN,MAAM;wCAClB;wCAGN,IAAIuB,KAAKxB,GAAG,CAAC2B,UAAU,CAAChB,uBAAuBgB,QAAQ;4CACrD,EAAE6B;4CACFlC,EAAE8B,MAAM;wCACV;oCACF;oCAEA,IACEY,gBAAgBR,SAChBtB,QAAQzB,GAAG,CAAC,cAAcsC,MAAM,GAAG,GACnC;wCACAf,aAAaoB,MAAM;oCACrB;gCACF,OAAO,IAAIpB,aAAa1C,IAAI,CAACoB,EAAE,CAACH,IAAI,KAAK,gBAAgB;oCACvD,MAAM2B,UAAUF,aAAavB,GAAG,CAC9B;oCAGF,MAAMuD,cAAcR;oCACpB,MAAMnB,WAAWH,QAAQzB,GAAG,CAAC;oCAC7B4B,SAASD,OAAO,CAAC,CAACE;4CAEZA,SAEOA;wCAHX,IAAIX;wCACJ,IAAIW,EAAAA,UAAAA,EAAEhD,IAAI,qBAANgD,QAAQ/B,IAAI,MAAK,cAAc;4CACjCoB,QAAQW;wCACV,OAAO,IAAIA,EAAAA,WAAAA,EAAEhD,IAAI,qBAANgD,SAAQ/B,IAAI,MAAK,eAAe;4CACzCoB,QAAQW,EAAE7B,GAAG,CACX;wCAEJ,OAAO;4CACL;wCACF;wCAEA,IAAIe,KAAKxB,GAAG,CAAC2B,UAAU,CAAChB,uBAAuBgB,QAAQ;4CACrD,EAAE6B;4CACFlB,EAAEc,MAAM;wCACV;oCACF;oCAEA,IACEY,gBAAgBR,SAChBtB,QAAQzB,GAAG,CAAC,YAAYsC,MAAM,GAAG,GACjC;wCACAf,aAAaoB,MAAM;oCACrB;gCACF;4BACF;4BACAb,qBAAqBkB;4BACrBjB,oBAAoBiB;4BACpBhB,yBAAyBgB;4BACzBf,iBAAiBmB;4BACjBlB,wBAAwBkB;4BACxBjB,0BAA0BiB;wBAC5B;oBACF,QAASL,OAAM;oBAEftF,kBAAkBC,GAAGC,MAAMC;gBAC7B;YACF;QACF;IACF;AACF"}