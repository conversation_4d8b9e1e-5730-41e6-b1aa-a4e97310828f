# Wuzhen-MOSS-TTSD 开发指南

## 1. 开发环境搭建

### 1.1 系统要求

```yaml
操作系统: Ubuntu 20.04+ / macOS 12+ / Windows 11
Python: 3.10+
Node.js: 16+ (用于前端开发工具)
Git: 2.30+
Docker: 20.10+ (可选)
GPU: NVIDIA GPU with CUDA 11.8+ (推荐)
```

### 1.2 环境配置

#### 克隆项目
```bash
# 克隆主项目
git clone <repository-url>
cd wuzhen-moss-ttsd

# 初始化 MOSS-TTSD 子模块
git submodule update --init --recursive
```

#### Python 环境
```bash
# 使用 pyenv 管理 Python 版本
pyenv install 3.10.12
pyenv local 3.10.12

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# venv\Scripts\activate  # Windows

# 升级 pip
pip install --upgrade pip

# 安装开发依赖
pip install -r requirements.txt
pip install -r requirements-dev.txt
pip install flash-attn --no-build-isolation
```

#### 预提交钩子
```bash
# 安装 pre-commit
pre-commit install

# 手动运行检查
pre-commit run --all-files
```

### 1.3 IDE 配置

#### VS Code 配置

**.vscode/settings.json**
```json
{
  "python.defaultInterpreterPath": "./venv/bin/python",
  "python.linting.enabled": true,
  "python.linting.pylintEnabled": true,
  "python.linting.mypyEnabled": true,
  "python.formatting.provider": "black",
  "python.sortImports.args": ["--profile", "black"],
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true
  },
  "files.exclude": {
    "**/__pycache__": true,
    "**/*.pyc": true,
    ".mypy_cache": true,
    ".pytest_cache": true
  }
}
```

**.vscode/launch.json**
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "FastAPI Debug",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/src/main.py",
      "console": "integratedTerminal",
      "env": {
        "PYTHONPATH": "${workspaceFolder}",
        "DEBUG": "true"
      }
    },
    {
      "name": "Pytest Debug",
      "type": "python",
      "request": "launch",
      "module": "pytest",
      "args": ["${workspaceFolder}/tests", "-v"],
      "console": "integratedTerminal",
      "env": {
        "PYTHONPATH": "${workspaceFolder}"
      }
    }
  ]
}
```

#### PyCharm 配置

1. 设置 Python 解释器为虚拟环境中的 Python
2. 配置代码风格为 Black
3. 启用 MyPy 类型检查
4. 配置测试运行器为 pytest

## 2. 项目结构详解

### 2.1 目录结构

```
wuzhen-moss-ttsd/
├── src/                        # 源代码目录
│   ├── __init__.py
│   ├── main.py                 # FastAPI 应用入口
│   │
│   ├── api/                    # API 接口层
│   │   ├── __init__.py
│   │   ├── dependencies.py     # 依赖注入
│   │   ├── middleware.py       # 中间件
│   │   └── routes/             # 路由模块
│   │       ├── __init__.py
│   │       ├── audio.py        # 音频生成接口
│   │       ├── podcast.py      # 播客生成接口
│   │       ├── tasks.py        # 任务管理接口
│   │       ├── files.py        # 文件管理接口
│   │       └── auth.py         # 认证接口
│   │
│   ├── core/                   # 核心模块
│   │   ├── __init__.py
│   │   ├── config.py           # 配置管理
│   │   ├── database.py         # 数据库连接
│   │   ├── redis.py            # Redis 连接
│   │   ├── security.py         # 安全相关
│   │   ├── exceptions.py       # 异常处理
│   │   ├── logging.py          # 日志配置
│   │   └── celery.py           # Celery 配置
│   │
│   ├── graphs/                 # LangGraph 工作流
│   │   ├── __init__.py
│   │   ├── base.py             # 基础图类
│   │   ├── script_generation.py # 脚本生成子图
│   │   ├── long_audio.py       # 长音频生成图
│   │   ├── dialogue.py         # 对话生成图
│   │   ├── voice_clone.py      # 语音克隆图
│   │   └── podcast.py          # 播客生成图
│   │
│   ├── agents/                 # 智能体实现
│   │   ├── __init__.py
│   │   ├── base.py             # 基础智能体类
│   │   ├── script_agent.py     # 脚本生成智能体
│   │   ├── audio_agent.py      # 音频处理智能体
│   │   └── quality_agent.py    # 质量检查智能体
│   │
│   ├── adapters/               # 适配器层
│   │   ├── __init__.py
│   │   ├── moss_ttsd.py        # MOSS-TTSD 适配器
│   │   └── siliconflow.py      # 硅基流动适配器
│   │
│   ├── models/                 # 数据模型
│   │   ├── __init__.py
│   │   ├── base.py             # 基础模型类
│   │   ├── requests.py         # 请求模型
│   │   ├── responses.py        # 响应模型
│   │   ├── database.py         # 数据库模型
│   │   └── enums.py            # 枚举类型
│   │
│   ├── services/               # 业务服务
│   │   ├── __init__.py
│   │   ├── base.py             # 基础服务类
│   │   ├── task_service.py     # 任务管理服务
│   │   ├── file_service.py     # 文件管理服务
│   │   ├── audio_service.py    # 音频处理服务
│   │   └── auth_service.py     # 认证服务
│   │
│   └── utils/                  # 工具函数
│       ├── __init__.py
│       ├── audio_utils.py      # 音频处理工具
│       ├── text_utils.py       # 文本处理工具
│       ├── file_utils.py       # 文件处理工具
│       └── validation.py       # 验证工具
│
├── moss_ttsd/                  # MOSS-TTSD 子模块
│   └── (MOSS-TTSD 源码)
│
├── tests/                      # 测试代码
│   ├── __init__.py
│   ├── conftest.py             # pytest 配置
│   ├── unit/                   # 单元测试
│   │   ├── test_services/
│   │   ├── test_adapters/
│   │   └── test_utils/
│   ├── integration/            # 集成测试
│   │   ├── test_api/
│   │   └── test_graphs/
│   └── e2e/                    # 端到端测试
│       └── test_workflows/
│
├── config/                     # 配置文件
│   ├── development.yaml
│   ├── production.yaml
│   ├── testing.yaml
│   └── logging.yaml
│
├── docs/                       # 文档
│   ├── architecture.md
│   ├── api.md
│   ├── deployment.md
│   ├── development.md
│   └── implementation-plan.md
│
├── scripts/                    # 脚本
│   ├── migrate.py              # 数据库迁移
│   ├── seed.py                 # 数据种子
│   ├── backup.sh               # 备份脚本
│   └── deploy.sh               # 部署脚本
│
├── docker/                     # Docker 配置
│   ├── Dockerfile
│   ├── docker-compose.yml
│   ├── nginx.conf
│   └── prometheus.yml
│
├── .github/                    # GitHub 配置
│   └── workflows/
│       ├── ci.yml              # 持续集成
│       └── cd.yml              # 持续部署
│
├── requirements.txt            # 生产依赖
├── requirements-dev.txt        # 开发依赖
├── pyproject.toml             # 项目配置
├── .env.example               # 环境变量示例
├── .gitignore                 # Git 忽略文件
├── .pre-commit-config.yaml    # 预提交配置
└── README.md                  # 项目说明
```

### 2.2 核心模块说明

#### API 层 (src/api/)
- **routes/**: 定义 RESTful API 端点
- **dependencies.py**: 依赖注入，如数据库连接、认证等
- **middleware.py**: 中间件，如 CORS、日志、限流等

#### 核心层 (src/core/)
- **config.py**: 统一配置管理，支持环境变量和配置文件
- **database.py**: 数据库连接和会话管理
- **security.py**: 认证、授权、加密等安全功能

#### 工作流层 (src/graphs/)
- **base.py**: 定义基础图类和通用功能
- **script_generation.py**: 脚本生成子图，被其他图调用
- **long_audio.py**: 长音频生成的完整工作流

#### 适配器层 (src/adapters/)
- **moss_ttsd.py**: 封装 MOSS-TTSD 模型调用
- **siliconflow.py**: 封装硅基流动大模型 API

#### 服务层 (src/services/)
- **task_service.py**: 任务生命周期管理
- **file_service.py**: 文件上传、下载、存储管理
- **audio_service.py**: 音频处理业务逻辑

## 3. 开发规范

### 3.1 代码风格

#### Python 代码规范
```python
# 使用 Black 格式化
# 行长度: 88 字符
# 使用双引号

# 导入顺序 (isort)
import os
import sys
from typing import Dict, List, Optional

import torch
import numpy as np
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel

from src.core.config import settings
from src.models.requests import AudioRequest

# 类定义
class AudioService:
    """音频处理服务
    
    提供音频生成、处理和管理功能。
    """
    
    def __init__(self, config: Dict[str, Any]) -> None:
        self.config = config
        self._initialized = False
    
    async def generate_audio(
        self, 
        text: str, 
        voice_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """生成音频
        
        Args:
            text: 要合成的文本
            voice_settings: 语音设置
            
        Returns:
            包含音频数据的字典
            
        Raises:
            AudioGenerationError: 音频生成失败时抛出
        """
        if not self._initialized:
            await self._initialize()
        
        # 实现逻辑
        pass

# 函数定义
def validate_audio_format(file_path: str) -> bool:
    """验证音频文件格式
    
    Args:
        file_path: 音频文件路径
        
    Returns:
        是否为有效格式
    """
    supported_formats = [".wav", ".mp3", ".flac"]
    return any(file_path.lower().endswith(fmt) for fmt in supported_formats)

# 常量定义
MAX_AUDIO_LENGTH = 3600  # 最大音频长度（秒）
SUPPORTED_SAMPLE_RATES = [16000, 22050, 24000, 44100, 48000]
DEFAULT_VOICE_SETTINGS = {
    "speed": 1.0,
    "pitch": 1.0,
    "volume": 1.0
}
```

#### 类型注解规范
```python
from typing import Dict, List, Optional, Union, Any, Callable
from pathlib import Path

# 基础类型
def process_text(text: str) -> str:
    pass

# 复杂类型
def process_batch(
    items: List[Dict[str, Any]], 
    callback: Optional[Callable[[str], None]] = None
) -> Dict[str, Union[str, int, float]]:
    pass

# 类属性注解
class TaskManager:
    tasks: Dict[str, Task]
    max_concurrent: int
    
    def __init__(self) -> None:
        self.tasks = {}
        self.max_concurrent = 10
```

### 3.2 错误处理

#### 自定义异常
```python
# src/core/exceptions.py
class WuzhenMOSSTTSDError(Exception):
    """基础异常类"""
    pass

class AudioGenerationError(WuzhenMOSSTTSDError):
    """音频生成异常"""
    pass

class VoiceCloneError(WuzhenMOSSTTSDError):
    """语音克隆异常"""
    pass

class TaskNotFoundError(WuzhenMOSSTTSDError):
    """任务不存在异常"""
    pass

# 使用示例
async def generate_audio(text: str) -> Dict[str, Any]:
    try:
        result = await moss_ttsd_adapter.generate_audio(text)
        return result
    except Exception as e:
        logger.error("Audio generation failed", error=str(e))
        raise AudioGenerationError(f"Failed to generate audio: {str(e)}")
```

#### 异常处理器
```python
# src/api/middleware.py
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse

async def exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """全局异常处理器"""
    if isinstance(exc, AudioGenerationError):
        return JSONResponse(
            status_code=422,
            content={
                "success": False,
                "error": {
                    "code": "AUDIO_GENERATION_FAILED",
                    "message": str(exc)
                }
            }
        )
    
    # 其他异常处理
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": {
                "code": "INTERNAL_ERROR",
                "message": "Internal server error"
            }
        }
    )
```

### 3.3 日志规范

#### 结构化日志
```python
import structlog

# 配置结构化日志
logger = structlog.get_logger()

# 使用示例
async def process_task(task_id: str) -> None:
    logger.info("Task processing started", task_id=task_id)
    
    try:
        # 处理逻辑
        result = await some_processing()
        logger.info(
            "Task processing completed", 
            task_id=task_id,
            duration=result.duration,
            output_size=result.size
        )
    except Exception as e:
        logger.error(
            "Task processing failed",
            task_id=task_id,
            error=str(e),
            exc_info=True
        )
        raise
```

### 3.4 测试规范

#### 单元测试
```python
# tests/unit/test_services/test_audio_service.py
import pytest
from unittest.mock import AsyncMock, patch

from src.services.audio_service import AudioService
from src.core.exceptions import AudioGenerationError

class TestAudioService:
    @pytest.fixture
    def audio_service(self):
        return AudioService(config={})
    
    @pytest.mark.asyncio
    async def test_generate_audio_success(self, audio_service):
        """测试音频生成成功"""
        with patch.object(audio_service, '_generate_audio') as mock_generate:
            mock_generate.return_value = {
                "audio_data": b"fake_audio_data",
                "sample_rate": 24000
            }
            
            result = await audio_service.generate_audio("test text")
            
            assert result["sample_rate"] == 24000
            mock_generate.assert_called_once_with("test text", None)
    
    @pytest.mark.asyncio
    async def test_generate_audio_failure(self, audio_service):
        """测试音频生成失败"""
        with patch.object(audio_service, '_generate_audio') as mock_generate:
            mock_generate.side_effect = Exception("Generation failed")
            
            with pytest.raises(AudioGenerationError):
                await audio_service.generate_audio("test text")
```

#### 集成测试
```python
# tests/integration/test_api/test_audio_routes.py
import pytest
from httpx import AsyncClient

from src.main import app

class TestAudioRoutes:
    @pytest.mark.asyncio
    async def test_generate_dialogue_audio(self):
        """测试对话音频生成接口"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.post(
                "/api/v1/audio/dialogue",
                json={
                    "text": "[S1]Hello[S2]Hi there",
                    "use_normalize": True
                },
                headers={"Authorization": "Bearer test_token"}
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert "task_id" in data["data"]
```

#### 端到端测试
```python
# tests/e2e/test_workflows/test_dialogue_generation.py
import pytest
from httpx import AsyncClient

class TestDialogueGenerationWorkflow:
    @pytest.mark.asyncio
    async def test_complete_dialogue_generation_workflow(self):
        """测试完整的对话生成工作流"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # 1. 创建任务
            create_response = await client.post(
                "/api/v1/audio/dialogue",
                json={"text": "[S1]Hello[S2]Hi there"}
            )
            task_id = create_response.json()["data"]["task_id"]
            
            # 2. 等待任务完成
            import asyncio
            for _ in range(30):  # 最多等待30秒
                status_response = await client.get(f"/api/v1/tasks/{task_id}")
                status = status_response.json()["data"]["status"]
                
                if status == "completed":
                    break
                elif status == "failed":
                    pytest.fail("Task failed")
                
                await asyncio.sleep(1)
            
            # 3. 获取结果
            result_response = await client.get(f"/api/v1/tasks/{task_id}/result")
            result = result_response.json()
            
            assert result["success"] is True
            assert "audio_url" in result["data"]["result"]
```

## 4. 开发工作流

### 4.1 Git 工作流

#### 分支策略
```bash
# 主分支
main          # 生产环境代码
develop       # 开发环境代码

# 功能分支
feature/audio-generation      # 音频生成功能
feature/voice-cloning        # 语音克隆功能
feature/podcast-generation   # 播客生成功能

# 修复分支
bugfix/memory-leak          # 内存泄漏修复
hotfix/security-patch       # 安全补丁
```

#### 提交规范
```bash
# 提交消息格式
<type>(<scope>): <subject>

<body>

<footer>

# 类型说明
feat:     新功能
fix:      修复bug
docs:     文档更新
style:    代码格式调整
refactor: 重构
test:     测试相关
chore:    构建过程或辅助工具的变动

# 示例
feat(audio): add voice cloning functionality

- Implement voice cloning using reference audio
- Add speaker similarity validation
- Update API documentation

Closes #123
```

#### 开发流程
```bash
# 1. 从 develop 创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/new-feature

# 2. 开发和提交
git add .
git commit -m "feat(scope): add new feature"

# 3. 推送分支
git push origin feature/new-feature

# 4. 创建 Pull Request
# 在 GitHub/GitLab 上创建 PR，请求合并到 develop

# 5. 代码审查和合并
# 经过代码审查后合并到 develop

# 6. 部署到测试环境
# develop 分支自动部署到测试环境

# 7. 发布到生产环境
git checkout main
git merge develop
git tag v1.0.0
git push origin main --tags
```

### 4.2 代码审查

#### 审查清单
```markdown
## 代码审查清单

### 功能性
- [ ] 功能是否按需求实现
- [ ] 边界条件是否处理
- [ ] 错误处理是否完善
- [ ] 性能是否满足要求

### 代码质量
- [ ] 代码是否清晰易读
- [ ] 命名是否规范
- [ ] 是否有重复代码
- [ ] 是否遵循设计模式

### 测试
- [ ] 是否有足够的单元测试
- [ ] 测试覆盖率是否达标
- [ ] 是否有集成测试
- [ ] 测试用例是否充分

### 安全性
- [ ] 是否有安全漏洞
- [ ] 输入验证是否充分
- [ ] 敏感信息是否泄露
- [ ] 权限控制是否正确

### 文档
- [ ] API 文档是否更新
- [ ] 代码注释是否充分
- [ ] README 是否更新
- [ ] 变更日志是否记录
```

### 4.3 持续集成

#### GitHub Actions 配置
```yaml
# .github/workflows/ci.yml
name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - uses: actions/checkout@v3
      with:
        submodules: recursive
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
    
    - name: Cache dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements*.txt') }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: Lint with pylint
      run: |
        pylint src/
    
    - name: Type check with mypy
      run: |
        mypy src/
    
    - name: Test with pytest
      run: |
        pytest tests/ --cov=src --cov-report=xml
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml

  build:
    needs: test
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
      with:
        submodules: recursive
    
    - name: Build Docker image
      run: |
        docker build -t wuzhen-moss-ttsd:${{ github.sha }} .
    
    - name: Run security scan
      run: |
        docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
          -v $PWD:/root/.cache/ aquasec/trivy:latest image \
          wuzhen-moss-ttsd:${{ github.sha }}
```

## 5. 调试指南

### 5.1 本地调试

#### FastAPI 调试
```python
# src/main.py
import uvicorn
from src.core.config import settings

if __name__ == "__main__":
    uvicorn.run(
        "src.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,  # 开发模式下启用热重载
        debug=settings.DEBUG,
        log_level="debug" if settings.DEBUG else "info"
    )
```

#### 数据库调试
```python
# 启用 SQL 日志
import logging
logging.getLogger('sqlalchemy.engine').setLevel(logging.INFO)

# 或在配置中设置
DATABASE_URL = "postgresql://user:pass@localhost/db?echo=true"
```

#### Redis 调试
```bash
# 连接 Redis 客户端
redis-cli

# 查看所有键
KEYS *

# 查看任务队列
LLEN celery
LRANGE celery 0 -1

# 监控命令
MONITOR
```

### 5.2 性能调试

#### 性能分析
```python
# 使用 cProfile
import cProfile
import pstats

def profile_function():
    # 要分析的代码
    pass

# 运行分析
cProfile.run('profile_function()', 'profile_stats')
stats = pstats.Stats('profile_stats')
stats.sort_stats('cumulative').print_stats(10)
```

#### 内存分析
```python
# 使用 memory_profiler
from memory_profiler import profile

@profile
def memory_intensive_function():
    # 内存密集型操作
    pass

# 运行: python -m memory_profiler script.py
```

#### GPU 监控
```bash
# 实时监控 GPU 使用情况
nvidia-smi -l 1

# 查看 GPU 进程
nvidia-smi pmon

# 查看详细信息
nvidia-smi -q -d MEMORY,UTILIZATION
```

### 5.3 日志调试

#### 日志配置
```python
# src/core/logging.py
import structlog
import logging.config

def setup_logging(log_level: str = "INFO"):
    """配置结构化日志"""
    
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, log_level.upper())
    )
```

#### 日志使用
```python
import structlog

logger = structlog.get_logger()

# 基础日志
logger.info("Processing started")

# 带上下文的日志
logger.info("Task processing", task_id="123", user_id="456")

# 错误日志
try:
    risky_operation()
except Exception as e:
    logger.error("Operation failed", error=str(e), exc_info=True)

# 性能日志
import time
start_time = time.time()
# ... 操作 ...
duration = time.time() - start_time
logger.info("Operation completed", duration=duration)
```

## 6. 性能优化

### 6.1 代码优化

#### 异步编程
```python
import asyncio
import aiohttp
from concurrent.futures import ThreadPoolExecutor

# 并发处理
async def process_multiple_tasks(tasks):
    """并发处理多个任务"""
    semaphore = asyncio.Semaphore(10)  # 限制并发数
    
    async def process_single_task(task):
        