{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/hot-linked-text/get-words-and-whitespaces.ts"], "names": ["getWordsAndWhitespaces", "isWhitespace", "char", "text", "wordsAndWhitespaces", "current", "currentIsWhitespace", "length", "nextIsWhitespace", "push"], "mappings": "AAAA,kFAAkF;;;;;+BAUlEA;;;eAAAA;;;AAThB,SAASC,aAAaC,IAAY;IAChC,OAAOA,SAAS,OAAOA,SAAS,QAAQA,SAAS,OAAQA,SAAS;AACpE;AAOO,SAASF,uBAAuBG,IAAY;IACjD,MAAMC,sBAAgC,EAAE;IAExC,IAAIC,UAAU;IACd,IAAIC,sBAAsB;IAC1B,KAAK,MAAMJ,QAAQC,KAAM;QACvB,IAAIE,QAAQE,MAAM,KAAK,GAAG;YACxBF,WAAWH;YACXI,sBAAsBL,aAAaC;YACnC;QACF;QAEA,MAAMM,mBAAmBP,aAAaC;QACtC,IAAII,wBAAwBE,kBAAkB;YAC5CH,WAAWH;QACb,OAAO;YACLE,oBAAoBK,IAAI,CAACJ;YACzBA,UAAUH;YACVI,sBAAsBE;QACxB;IACF;IAEA,IAAIH,QAAQE,MAAM,GAAG,GAAG;QACtBH,oBAAoBK,IAAI,CAACJ;IAC3B;IAEA,OAAOD;AACT"}